function [t, signal_output, clks, clkh, digital_data_struct, analog_data_struct] = generate_timebase(params)
%GENERATE_TIMEBASE 生成固定时间步长的时间基准和信号矩阵
%   版本历史:
%   v1.0 - 标准化时间索引结构体重构版本
%   v1.1 - 添加斜坡信号生成功能，支持静态特性测试
%   
%   重构要点:
%   1. 固定时间间隔：1e-11秒(10ps，采样周期的1/1000)
%   2. 输入信号长度：动态计算（正弦波15μs，斜坡信号基于码密度分析法）
%   3. 结构体存储：数字18位独立存储 + 模拟9条线路独立存储
%   4. 非交叠时钟：CLKS/CLKH (0/1电平)
%   5. 时间索引与数据结构体严格对应
%   6. 支持正弦波和斜坡信号生成
%
%   输入参数:
%       params - ADC参数结构体
%           fs - 采样频率 (Hz)
%           f_in - 输入信号频率 (Hz) 
%           Vref - 参考电压 (V)
%           Vcm - 共模电压 (V)
%           signal_type - 信号类型 ('sine' 或 'ramp')，默认为'sine'
%           resolution - ADC分辨率 (bit，用于斜坡信号长度计算)
%
%   输出参数:
%       t - 固定步长时间向量
%       signal_output - 输出信号（正弦波或斜坡信号）
%       clks - CLKS时钟信号 (0/1电平)
%       clkh - CLKH时钟信号 (0/1电平)
%       digital_data_struct - 数字数据结构体 (18位独立存储)
%       analog_data_struct - 模拟数据结构体 (9条线路独立存储)

    fprintf('=== 固定时间步长信号生成 ===\n');
    
    %% 参数设置和默认值
    fs_adc = params.fs;                    % ADC采样率 
    f_clk = fs_adc;                        % 时钟频率 = 采样频率
    f_signal = params.f_in;                % 信号频率
    A_signal = 1 * params.Vref;            % 信号振幅，满量程
    
    % 信号类型设置（默认为正弦波）
    if isfield(params, 'signal_type')
        signal_type = params.signal_type;
    else
        signal_type = 'sine';
    end
    
    % ADC分辨率（用于斜坡信号长度计算）
    if isfield(params, 'resolution')
        N_bits = params.resolution;
    else
        N_bits = 10;  % 默认10位
    end
    
    fprintf('信号类型: %s\n', signal_type);
    
    % 固定时间步长参数
    dt_fixed = 1e-11;                      % 10ps固定时间步长
    
    %% 根据信号类型计算仿真时长
    if strcmp(signal_type, 'ramp')
        % 斜坡信号：基于码密度分析法计算
        t_total = calculate_ramp_signal_duration(fs_adc, f_signal, N_bits);
        fprintf('斜坡信号仿真时长: %.2f μs (基于码密度分析法)\n', t_total*1e6);
    else
        % 正弦波信号：使用原有15μs + 缓冲设置
        t_signal = 15e-6;                      % 15μs输入信号长度
        t_buffer = 5 * 10e-9;                  % 5×10ns预留空间
        t_total = t_signal + t_buffer;         % 总仿真时长
        fprintf('正弦信号仿真时长: %.2f μs\n', t_total*1e6);
    end

%   时钟参数（贴近晶体管仿真）
    clks_width = 0.45/fs_adc;              % CLKS脉冲宽度: 45%占空比
    clks_delay = 0.05/fs_adc;              % CLKS相位延迟: 0.5ns
    clkh_width = 0.45/fs_adc;              % CLKH脉冲宽度: 45%占空比  
    clkh_delay = 0.55/fs_adc;              % CLKH相位延迟: 5.5ns (确保非交叠) 

%{
%   时钟参数（贴近simulink仿真）
%   clks_width = 0.48/fs_adc;              % CLKS脉冲宽度: 48%占空比
%   clks_delay = 0.05/fs_adc;              % CLKS相位延迟: 0.5ns
%   clkh_width = 0.48/fs_adc;              % CLKH脉冲宽度: 48%占空比  
%   clkh_delay = 0.55/fs_adc;              % CLKH相位延迟: 5.5ns (确保非交叠) 
%}

    %% 生成固定步长时间向量
    t = 0:dt_fixed:t_total;
    num_samples = length(t);
    
    fprintf('  总时间点数: %d\n', num_samples);
    fprintf('  时钟频率: %.1f MHz\n', f_clk/1e6);
    
    %% 生成输入信号
    if strcmp(signal_type, 'ramp')
        % 生成斜坡信号
        signal_output = generate_ramp_signal(t, f_signal, A_signal);
        fprintf('  斜坡信号: 频率%.3f MHz, 振幅±%.3f V\n', f_signal/1e6, A_signal);
    else
        % 生成正弦波信号（保留原有代码）
        signal_output = A_signal * sin(2*pi*f_signal*t);
        fprintf('  正弦信号: 频率%.3f MHz, 振幅±%.3f V\n', f_signal/1e6, A_signal);
    end
    
    %% 生成非交叠时钟信号 (0/1电平)
    % 初始化时钟信号
    clks = zeros(size(t));
    clkh = zeros(size(t));
    
    num_clk_cycles = ceil(t_total * f_clk) + 2;
    
    % 生成CLKS时钟
    for n = 0:num_clk_cycles
        rise_time = n/f_clk + clks_delay;
        fall_time = rise_time + clks_width;
        
        if rise_time >= 0 && rise_time <= t_total
            rise_idx = find(t >= rise_time, 1, 'first');
            fall_idx = find(t >= fall_time, 1, 'first');
            
            if ~isempty(rise_idx) && ~isempty(fall_idx) && fall_idx <= length(t)
                clks(rise_idx:fall_idx) = 1;
            end
        end
    end
    
    % 生成CLKH时钟  
    for n = 0:num_clk_cycles
        rise_time = n/f_clk + clkh_delay;
        fall_time = rise_time + clkh_width;
        
        if rise_time >= 0 && rise_time <= t_total
            rise_idx = find(t >= rise_time, 1, 'first');
            fall_idx = find(t >= fall_time, 1, 'first');
            
            if ~isempty(rise_idx) && ~isempty(fall_idx) && fall_idx <= length(t)
                clkh(rise_idx:fall_idx) = 1;
            end
        end
    end
    
    %% 创建数字数据结构体（18位原始数字信号独立存储）
    % 创建基础数字数据结构体
    digital_data_struct = struct();
    
    % 添加时间向量 (列向量)
    digital_data_struct.time = t(:);
    
    % 预分配18位数字信号结构体数组
    digital_signals(18) = struct();
    
    % 为18位数字数据创建独立的信号结构体
    for bit_idx = 1:18
        digital_signals(bit_idx).values = zeros(num_samples, 1);           % 初始化为零（列向量）
        digital_signals(bit_idx).dimensions = 1;                           % 单维信号
        digital_signals(bit_idx).label = sprintf('Digital_Bit_%02d', bit_idx);  % 位标签
        digital_signals(bit_idx).blockName = sprintf('Pipeline/Stage_%d/Bit_%d', ...
                                           ceil(bit_idx/2), mod(bit_idx-1,2)+1);  % 模块路径
    end
    
    digital_data_struct.signals = digital_signals;
    
    %% 创建模拟数据结构体（9条模拟数据线独立存储）
    % 创建基础模拟数据结构体
    analog_data_struct = struct();
    
    % 添加时间向量 (列向量)
    analog_data_struct.time = t(:);
    
    % 模拟数据线标签定义
    analog_labels = {'SHA_Output', 'Stage1_Output', 'Stage2_Output', 'Stage3_Output', ...
                     'Stage4_Output', 'Stage5_Output', 'Stage6_Output', 'Stage7_Output', 'Stage8_Output'};
    
    % 预分配9条模拟数据线结构体数组
    analog_signals(9) = struct();
    
    % 为9条模拟数据线创建独立的信号结构体
    for line_idx = 1:9
        analog_signals(line_idx).values = zeros(num_samples, 1);           % 初始化为零（列向量）
        analog_signals(line_idx).dimensions = 1;                           % 单维信号
        analog_signals(line_idx).label = analog_labels{line_idx};          % 数据线标签
        analog_signals(line_idx).blockName = sprintf('Pipeline/%s', analog_labels{line_idx});  % 模块路径
    end
    
    analog_data_struct.signals = analog_signals;
    
    %% 确保输出格式一致性（列向量）
    t = t(:);
    signal_output = signal_output(:);
    clks = clks(:);
    clkh = clkh(:);
    
end 

function t_total = calculate_ramp_signal_duration(fs_adc, f_ramp, N_bits)
%CALCULATE_RAMP_SIGNAL_DURATION 计算斜坡信号仿真时长
%   基于码密度分析法样本需求公式计算所需采样点数，
%   然后计算对应的仿真时长，确保整数周期的相干采样
%
%   输入参数:
%       fs_adc - ADC采样频率 (Hz)
%       f_ramp - 斜坡信号频率 (Hz)
%       N_bits - ADC分辨率 (bit)
%
%   输出参数:
%       t_total - 计算得到的仿真时长 (秒)

    % 码密度分析法样本需求公式参数
    beta_lsb = 0.2;                    % DNL测量精度 (LSB)
    Z_alpha_half = 1.96;               % 95%置信度系数
    
    % 计算所需采样点数
    % N_record = π × 2^(N-1) × (Z_α/2)² / β²
    N_record = pi * 2^(N_bits-1) * (Z_alpha_half^2) / (beta_lsb^2);
    N_record = ceil(N_record);         % 向上取整
    
    % 基于采样频率计算需求时间
    t_required = N_record / fs_adc;
    
    % 计算斜坡信号周期
    T_ramp = 1 / f_ramp;
    
    % 计算所需整数周期数（向上取整确保相干采样）
    N_cycles = ceil(t_required / T_ramp);
    
    % 计算最终仿真时长
    t_total = N_cycles * T_ramp;
    
    % 验证采样点数是否足够
    final_samples = ceil(t_total * fs_adc);
    
    fprintf('码密度分析法参数:\n');
    fprintf('  理论需求采样点: %d\n', N_record);
    fprintf('  斜坡信号周期数: %d\n', N_cycles);
    fprintf('  实际仿真采样点: %d\n', final_samples);
    
end

function ramp_signal = generate_ramp_signal(t, f_ramp, amplitude)
%GENERATE_RAMP_SIGNAL 生成线性斜坡信号
%   生成严格线性的锯齿波信号，电压范围为[-amplitude, +amplitude]
%   确保在指定频率下的相干采样和线性度要求
%
%   输入参数:
%       t - 时间向量 (秒)
%       f_ramp - 斜坡信号频率 (Hz)
%       amplitude - 信号振幅 (V)，输出范围为[-amplitude, +amplitude]
%
%   输出参数:
%       ramp_signal - 生成的斜坡信号向量

    % 计算斜坡信号周期
    T_ramp = 1 / f_ramp;
    
    % 生成标准锯齿波（0到1范围）
    t_normalized = mod(t, T_ramp) / T_ramp;
    
    % 将0到1范围映射到-amplitude到+amplitude
    ramp_signal = 2 * amplitude * (t_normalized - 0.5);
    
    fprintf('斜坡信号生成完成: 周期%.3f μs, 幅度±%.3f V\n', T_ramp*1e6, amplitude);
    
end 
