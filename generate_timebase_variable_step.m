function [t, signal_output, clks, clkh, digital_data_struct, analog_data_struct] = generate_timebase_variable_step(params)
%GENERATE_TIMEBASE_VARIABLE_STEP 生成可变步长时间基准和信号矩阵
%   版本历史:
%   v1.0 - 基于事件驱动处理需求的可变精度时间索引生成
%   v2.0 - 采用周期性模板算法，单周期可变步长模板快速拼接
%   
%   时间精度策略:
%   - 高精度窗口: 时钟边沿前后±50ps范围内使用5ps时间步长
%   - 低精度区域: 时钟稳定状态使用100ps时间步长
%   - 周期性处理: 生成单个采样周期模板后批量拼接
%
%   输入参数:
%       params - ADC参数结构体
%           fs - 采样频率 (Hz)
%           f_in - 输入信号频率 (Hz) 
%           Vref - 参考电压 (V)
%           Vcm - 共模电压 (V)
%           signal_type - 信号类型 ('sine' 或 'ramp')，默认为'sine'
%           resolution - ADC分辨率 (bit，用于斜坡信号长度计算)
%
%   输出参数:
%       t - 可变步长时间向量
%       signal_output - 输出信号（正弦波或斜坡信号）
%       clks - CLKS时钟信号 (0/1电平)
%       clkh - CLKH时钟信号 (0/1电平)
%       digital_data_struct - 数字数据结构体 (18位独立存储)
%       analog_data_struct - 模拟数据结构体 (9条线路独立存储)

    fprintf('=== 可变步长时间索引信号生成 ===\n');
    
    %% 参数设置和默认值
    fs_adc = params.fs;                    % ADC采样率 
    f_clk = fs_adc;                        % 时钟频率 = 采样频率
    f_signal = params.f_in;                % 信号频率
    A_signal = 1 * params.Vref;            % 信号振幅，满量程
    
    % 信号类型设置（默认为正弦波）
    if isfield(params, 'signal_type')
        signal_type = params.signal_type;
    else
        signal_type = 'sine';
    end
    
    % ADC分辨率（用于斜坡信号长度计算）
    if isfield(params, 'resolution')
        N_bits = params.resolution;
    else
        N_bits = 10;  % 默认10位
    end
    
    fprintf('信号类型: %s\n', signal_type);
    
    % 可变步长参数
    dt_high_precision = 5e-12;             % 5ps高精度时间步长
    dt_low_precision = 100e-12;            % 100ps低精度时间步长
    edge_window = 50e-12;                  % ±50ps边沿高精度窗口
    
    %% 根据信号类型计算仿真时长
    if strcmp(signal_type, 'ramp')
        % 斜坡信号：基于码密度分析法计算
        t_total = calculate_ramp_duration(fs_adc, f_signal, N_bits);
        fprintf('斜坡信号仿真时长: %.2f μs\n', t_total*1e6);
    else
        % 正弦波信号：使用原有15μs + 缓冲设置
        t_signal = 15e-6;                      % 15μs输入信号长度
        t_buffer = 5 * 10e-9;                  % 5×10ns预留空间
        t_total = t_signal + t_buffer;         % 总仿真时长
        fprintf('正弦信号仿真时长: %.2f μs\n', t_total*1e6);
    end

    % 时钟参数（贴近晶体管仿真）
    clks_width = 0.45/fs_adc;              % CLKS脉冲宽度: 45%占空比
    clks_delay = 0.05/fs_adc;              % CLKS相位延迟: 0.5ns
    clkh_width = 0.45/fs_adc;              % CLKH脉冲宽度: 45%占空比  
    clkh_delay = 0.55/fs_adc;              % CLKH相位延迟: 5.5ns (确保非交叠) 

    %% 生成可变步长时间向量（周期性模板算法）
    clock_params = struct('fs', f_clk, 'clks_delay', clks_delay, 'clks_width', clks_width, 'clkh_delay', clkh_delay, 'clkh_width', clkh_width);
    precision_params = struct('dt_high', dt_high_precision, 'dt_low', dt_low_precision, 'edge_window', edge_window);
    
    t = periodic_variable_timestep(t_total, clock_params, precision_params);
    num_samples = length(t);
    
    fprintf('  总时间点数: %d\n', num_samples);
    fprintf('  时钟频率: %.1f MHz\n', f_clk/1e6);
    
    %% 生成输入信号
    if strcmp(signal_type, 'ramp')
        % 生成斜坡信号
        signal_output = generate_ramp_signal(t, f_signal, A_signal);
        fprintf('  斜坡信号: 频率%.3f MHz, 振幅±%.3f V\n', f_signal/1e6, A_signal);
    else
        % 生成正弦波信号
        signal_output = A_signal * sin(2*pi*f_signal*t);
        fprintf('  正弦信号: 频率%.3f MHz, 振幅±%.3f V\n', f_signal/1e6, A_signal);
    end
    
    %% 生成非交叠时钟信号 (0/1电平)
    [clks, clkh] = create_periodic_clocks(t, clock_params);
    
    %% 创建数字数据结构体（18位原始数字信号独立存储）
    digital_data_struct = create_digital_data_structure(t, num_samples);
    
    %% 创建模拟数据结构体（9条模拟数据线独立存储）
    analog_data_struct = create_analog_data_structure(t, num_samples);
    
    %% 确保输出格式一致性（列向量）
    t = t(:);
    signal_output = signal_output(:);
    clks = clks(:);
    clkh = clkh(:);
    
end 

function t_total = calculate_ramp_duration(fs_adc, f_ramp, N_bits)
%CALCULATE_RAMP_DURATION 计算斜坡信号仿真时长
%   基于码密度分析法样本需求公式计算仿真时长

    % 码密度分析法样本需求公式参数
    beta_lsb = 0.2;                    % DNL测量精度 (LSB)
    Z_alpha_half = 1.96;               % 95%置信度系数
    
    % 计算所需采样点数
    N_record = pi * 2^(N_bits-1) * (Z_alpha_half^2) / (beta_lsb^2);
    N_record = ceil(N_record);         % 向上取整
    
    % 基于采样频率计算需求时间
    t_required = N_record / fs_adc;
    
    % 计算斜坡信号周期
    T_ramp = 1 / f_ramp;
    
    % 计算所需整数周期数（向上取整确保相干采样）
    N_cycles = ceil(t_required / T_ramp);
    
    % 计算最终仿真时长
    t_total = N_cycles * T_ramp;
    
    % 验证采样点数是否足够
    final_samples = ceil(t_total * fs_adc);
    
    fprintf('码密度分析法: 需求采样点%d, 斜坡周期数%d, 实际采样点%d\n', ...
            N_record, N_cycles, final_samples);
    
end

function ramp_signal = generate_ramp_signal(t, f_ramp, amplitude)
%GENERATE_RAMP_SIGNAL 生成线性斜坡信号
%   生成锯齿波信号，电压范围为[-amplitude, +amplitude]

    % 计算斜坡信号周期
    T_ramp = 1 / f_ramp;
    
    % 生成标准锯齿波（0到1范围）
    t_normalized = mod(t, T_ramp) / T_ramp;
    
    % 将0到1范围映射到-amplitude到+amplitude
    ramp_signal = 2 * amplitude * (t_normalized - 0.5);
    
    fprintf('斜坡信号生成: 周期%.3f μs, 幅度±%.3f V\n', T_ramp*1e6, amplitude);
    
end 

function t = periodic_variable_timestep(t_total, clock_params, precision_params)
%CREATE_PERIODIC_VARIABLE_TIMESTEP 使用周期性模板生成可变步长时间向量
%   生成单个采样周期的可变步长模板，然后批量拼接多个周期
%   简化版本：使用ceil()直接计算目标周期数，接受轻微的时间偏差

    % 提取参数
    fs = clock_params.fs;
    T_cycle = 1 / fs;  % 采样周期 (10ns)
    
    % 生成单个周期的可变步长时间模板
    cycle_template = create_cycle_template(clock_params, precision_params);
    
    % 简化算法：直接使用ceil计算目标周期数
    num_target_cycles = ceil(t_total / T_cycle);
    
    % 检查周期数有效性
    if num_target_cycles <= 0
        error('目标周期数必须大于0，当前计算得到：%d', num_target_cycles);
    end
    
    % 批量拼接所有周期（合并原assemble_complete_cycles功能）
    template_length = length(cycle_template);
    total_samples = num_target_cycles * template_length;
    
    % 预分配时间向量
    t = zeros(1, total_samples);
    
    % 批量生成时间偏移
    for cycle = 0:(num_target_cycles-1)
        start_idx = cycle * template_length + 1;
        end_idx = (cycle + 1) * template_length;
        
        % 应用时间偏移：template + cycle * T_cycle
        t(start_idx:end_idx) = cycle_template + cycle * T_cycle;
    end
    
end

function cycle_template = create_cycle_template(clock_params, precision_params)
%CREATE_CYCLE_TEMPLATE 创建单个采样周期的可变步长时间模板
%   在单个10ns周期内生成包含时钟事件高精度窗口的时间点序列

    % 提取参数
    fs = clock_params.fs;
    T_cycle = 1 / fs;
    clks_delay = clock_params.clks_delay;
    clks_width = clock_params.clks_width;
    clkh_delay = clock_params.clkh_delay;
    clkh_width = clock_params.clkh_width;
    
    dt_high = precision_params.dt_high;
    dt_low = precision_params.dt_low;
    edge_window = precision_params.edge_window;
    
    % 计算单个周期内的4个时钟事件时刻
    clks_rise = clks_delay;
    clks_fall = clks_delay + clks_width;
    clkh_rise = clkh_delay;
    clkh_fall = clkh_delay + clkh_width;
    
    % 收集周期内所有边沿事件
    edge_times = [clks_rise, clks_fall, clkh_rise, clkh_fall];
    edge_times = sort(edge_times);  % 按时间排序
    
    % 估算单周期所需时间点数并预分配数组
    max_points_estimate = ceil(T_cycle / dt_high) + 10;  % 保守估计
    cycle_template_temp = zeros(1, max_points_estimate);
    point_count = 0;
    current_time = 0;
    
    while current_time < T_cycle
        % 检查是否在高精度窗口内
        in_high_precision = false;
        for edge_time = edge_times
            if abs(current_time - edge_time) <= edge_window
                in_high_precision = true;
                break;
            end
        end
        
        % 添加当前时间点
        point_count = point_count + 1;
        cycle_template_temp(point_count) = current_time;
        
        % 选择下一步长
        if in_high_precision
            next_step = dt_high;
        else
            next_step = dt_low;
        end
        
        % 检查是否会超出周期边界
        if current_time + next_step >= T_cycle
            break;  % 在周期边界前结束，避免重复
        end
        
        current_time = current_time + next_step;
    end
    
    % 截取有效部分
    cycle_template = cycle_template_temp(1:point_count);
    
end

function [clks, clkh] = create_periodic_clocks(t, clock_params)
%CREATE_PERIODIC_CLOCKS 基于周期性参数生成非交叠时钟信号
%   利用周期性特征直接生成时钟信号，避免逐点计算

    % 提取参数
    fs = clock_params.fs;
    T_cycle = 1 / fs;
    clks_delay = clock_params.clks_delay;
    clks_width = clock_params.clks_width;
    clkh_delay = clock_params.clkh_delay;
    clkh_width = clock_params.clkh_width;
    
    % 初始化时钟信号
    clks = zeros(size(t));
    clkh = zeros(size(t));
    
    % 生成CLKS时钟：使用向量化操作
    for cycle = 0:floor(max(t) / T_cycle)
        cycle_start = cycle * T_cycle;
        
        % CLKS有效区间
        clks_start = cycle_start + clks_delay;
        clks_end = cycle_start + clks_delay + clks_width;
        
        % 使用逻辑索引设置CLKS
        clks(t >= clks_start & t <= clks_end) = 1;
        
        % CLKH有效区间
        clkh_start = cycle_start + clkh_delay;
        clkh_end = cycle_start + clkh_delay + clkh_width;
        
        % 使用逻辑索引设置CLKH
        clkh(t >= clkh_start & t <= clkh_end) = 1;
    end
    
end

function digital_data_struct = create_digital_data_structure(t, num_samples)
%CREATE_DIGITAL_DATA_STRUCTURE 创建数字数据结构体
%   创建18位数字信号的独立存储结构

    % 创建基础数字数据结构体
    digital_data_struct = struct();
    
    % 添加时间向量 (列向量)
    digital_data_struct.time = t(:);
    
    % 预分配18位数字信号结构体数组
    digital_signals(18) = struct();
    
    % 为18位数字数据创建独立的信号结构体
    for bit_idx = 1:18
        digital_signals(bit_idx).values = zeros(num_samples, 1);           % 初始化为零（列向量）
        digital_signals(bit_idx).dimensions = 1;                           % 单维信号
        digital_signals(bit_idx).label = sprintf('Digital_Bit_%02d', bit_idx);  % 位标签
        digital_signals(bit_idx).blockName = sprintf('Pipeline/Stage_%d/Bit_%d', ...
                                           ceil(bit_idx/2), mod(bit_idx-1,2)+1);  % 模块路径
    end
    
    digital_data_struct.signals = digital_signals;
    
end

function analog_data_struct = create_analog_data_structure(t, num_samples)
%CREATE_ANALOG_DATA_STRUCTURE 创建模拟数据结构体
%   创建9条模拟数据线的独立存储结构

    % 创建基础模拟数据结构体
    analog_data_struct = struct();
    
    % 添加时间向量 (列向量)
    analog_data_struct.time = t(:);
    
    % 模拟数据线标签定义
    analog_labels = {'SHA_Output', 'Stage1_Output', 'Stage2_Output', 'Stage3_Output', ...
                     'Stage4_Output', 'Stage5_Output', 'Stage6_Output', 'Stage7_Output', 'Stage8_Output'};
    
    % 预分配9条模拟数据线结构体数组
    analog_signals(9) = struct();
    
    % 为9条模拟数据线创建独立的信号结构体
    for line_idx = 1:9
        analog_signals(line_idx).values = zeros(num_samples, 1);           % 初始化为零（列向量）
        analog_signals(line_idx).dimensions = 1;                           % 单维信号
        analog_signals(line_idx).label = analog_labels{line_idx};          % 数据线标签
        analog_signals(line_idx).blockName = sprintf('Pipeline/%s', analog_labels{line_idx});  % 模块路径
    end
    
    analog_data_struct.signals = analog_signals;
    
end