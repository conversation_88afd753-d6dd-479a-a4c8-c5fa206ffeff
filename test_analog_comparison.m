function test_analog_comparison()
%TEST_ANALOG_COMPARISON 流水线ADC数据可视化对比
%   VERSION: v2.2 - 完整版：数据可视化 + 样本点误差分析 + 详细数据保存
%
%   功能描述:
%   1. 调用extract_pipeline_data()提取理想模型与晶体管仿真数据
%   2. 生成多层次可视化对比图表：
%      - 图1：各级流水线信号对比（理想 vs 仿真）
%      - 图2：各级样本点误差变化趋势
%      - 图3：各级误差统计对比分析
%   3. 保存详细对比数据为.mat文件（包含每个样本点的误差数据）
%
%   输出:
%   - 可视化窗口：3个专业可视化窗口
%   - 数据文件：保存到./test_data/目录，包含详细样本点误差数据

    fprintf('=== 流水线ADC数据可视化对比 ===\n');
    
    %% 第一步：数据提取
    fprintf('\n[步骤1/3] 数据提取...\n');
    try
        extracted_data = extract_pipeline_data();
        fprintf('✓ 数据提取成功，提取周期数: %d\n', extracted_data.extraction_info.extract_cycles);
    catch ME
        fprintf('✗ 数据提取失败: %s\n', ME.message);
        return;
    end
    
    %% 第二步：可视化对比
    fprintf('\n[步骤2/3] 生成可视化对比...\n');
    try
        create_comparison_visualization(extracted_data);
        fprintf('✓ 可视化对比生成完成\n');
    catch ME
        fprintf('✗ 可视化失败: %s\n', ME.message);
        return;
    end
    
    %% 第三步：保存数据
    fprintf('\n[步骤3/3] 保存对比数据...\n');
    try
        save_comparison_data(extracted_data);
        fprintf('✓ 数据保存完成\n');
    catch ME
        fprintf('✗ 数据保存失败: %s\n', ME.message);
        return;
    end
    
    fprintf('\n=== 流水线ADC数据对比完成 ===\n');
end

function create_comparison_visualization(extracted_data)
%CREATE_COMPARISON_VISUALIZATION 创建流水线各级信号对比可视化
%   生成理想模型与晶体管仿真数据的对比图表

    % 获取流水线级别名称
    stage_names = {'SHA', 'STAGE1', 'STAGE2', 'STAGE3', 'STAGE4', ...
                   'STAGE5', 'STAGE6', 'STAGE7', 'STAGE8'};
    
    % 创建主对比窗口（3×3网格布局）
    figure('Name', '流水线ADC各级信号对比 - 理想模型 vs 晶体管仿真', ...
           'Position', [100, 100, 1600, 1200], ...
           'Color', 'white');
    
    % 绘制各级对比子图
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        
        % 检查数据是否存在
        if ~isfield(extracted_data, stage_name)
            continue;
        end
        
        stage_data = extracted_data.(stage_name);
        ideal_signal = stage_data.ideal_signal;
        sim_signal = stage_data.sim_signal;
        
        % 创建子图
        subplot(3, 3, i);
        
        % 获取样本索引
        sample_indices = 1:length(ideal_signal);
        
        % 绘制理想信号和仿真信号
        plot(sample_indices, ideal_signal, 'b-', 'LineWidth', 1.8, 'DisplayName', '理想模型');
        hold on;
        plot(sample_indices, sim_signal, 'r--', 'LineWidth', 1.5, 'DisplayName', '晶体管仿真');
        hold off;
        
        % 设置图表属性
        xlabel('采样序号', 'FontSize', 10);
        ylabel('电压 (V)', 'FontSize', 10);
        title(stage_name, 'FontSize', 12, 'FontWeight', 'bold');
        legend('show', 'Location', 'best', 'FontSize', 9);
        grid on;
        
        % 计算简单的误差统计用于显示
        error_signal = sim_signal - ideal_signal;
        mae = mean(abs(error_signal));
        max_error = max(abs(error_signal));
        
        % 在子图上添加误差信息
        text(0.02, 0.98, sprintf('MAE: %.4f V\nMax: %.4f V', mae, max_error), ...
             'Units', 'normalized', 'VerticalAlignment', 'top', ...
             'FontSize', 8, 'BackgroundColor', 'white', 'EdgeColor', 'black');
    end
    
    % 添加总标题
    sgtitle('流水线ADC各级输出对比分析 - 理想模型与晶体管仿真', ...
            'FontSize', 16, 'FontWeight', 'bold');
    
    % 创建第二个窗口：各级样本点误差变化图
    figure('Name', '流水线ADC各级误差', ...
           'Position', [150, 120, 1600, 1200], ...
           'Color', 'white');
    
    % 获取时钟域信息
    clkh_stages = extracted_data.extraction_info.clkh_stages;
    
    % 绘制各级样本点误差变化子图
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        
        % 检查数据是否存在
        if ~isfield(extracted_data, stage_name)
            continue;
        end
        
        stage_data = extracted_data.(stage_name);
        ideal_signal = stage_data.ideal_signal;
        sim_signal = stage_data.sim_signal;
        
        % 计算每个样本点的误差
        error_signal = sim_signal - ideal_signal;
        sample_indices = 1:length(error_signal);
        
        % 创建子图
        subplot(3, 3, i);
        
        % 根据时钟域设置颜色
        if ismember(stage_name, clkh_stages)
            line_color = [0.2, 0.6, 0.8];  % 蓝色 - CLKH域
        else
            line_color = [0.8, 0.4, 0.2];  % 橙色 - CLKS域
        end
        
        % 绘制误差变化曲线
        plot(sample_indices, error_signal, '-', 'Color', line_color, ...
             'LineWidth', 1.5);
        
        % 添加零误差参考线
        hold on;
        plot([1, length(error_signal)], [0, 0], 'k--', 'LineWidth', 0.8);
        hold off;
        
        % 设置图表属性
        xlabel('采样序号', 'FontSize', 10);
        ylabel('误差 (V)', 'FontSize', 10);
        title(sprintf('%s 误差变化', stage_name), 'FontSize', 12, 'FontWeight', 'bold');
        grid on;
        
        % 计算误差统计信息
        mae = mean(abs(error_signal));
        max_error = max(abs(error_signal));
        min_error = min(abs(error_signal));
        std_error = std(error_signal);
        
        % 在子图上添加统计信息
        info_text = sprintf('MAE: %.4f V\nMax: %.4f V\nMin: %.4f V\nStd: %.4f V', ...
                           mae, max_error, min_error, std_error);
        text(0.02, 0.98, info_text, ...
             'Units', 'normalized', 'VerticalAlignment', 'top', ...
             'FontSize', 8, 'BackgroundColor', 'white', 'EdgeColor', 'black');
        
        % 设置Y轴范围为对称的，便于观察
        max_abs_error = max(abs(error_signal));
        if max_abs_error > 0
            ylim([-max_abs_error*1.1, max_abs_error*1.1]);
        end
    end
    
    % 添加总标题
    sgtitle('流水线ADC各级样本点误差变化分析 (误差 = 晶体管仿真值 - 理想模型值)', ...
            'FontSize', 16, 'FontWeight', 'bold');
    
    % 创建第三个窗口：各级误差对比柱状图
    figure('Name', '流水线ADC各级误差统计对比', ...
           'Position', [200, 150, 1200, 600], ...
           'Color', 'white');
    
    % 计算各级误差数据
    stage_mae = [];
    stage_max_error = [];
    stage_labels = {};
    colors = [];
    
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        
        if ~isfield(extracted_data, stage_name)
            continue;
        end
        
        stage_data = extracted_data.(stage_name);
        ideal_signal = stage_data.ideal_signal;
        sim_signal = stage_data.sim_signal;
        
        error_signal = sim_signal - ideal_signal;
        stage_mae(end+1) = mean(abs(error_signal));
        stage_max_error(end+1) = max(abs(error_signal));
        stage_labels{end+1} = stage_name;
        
        % 根据时钟域设置颜色
        if ismember(stage_name, clkh_stages)
            colors(end+1,:) = [0.2, 0.6, 0.8];  % 蓝色 - CLKH域
        else
            colors(end+1,:) = [0.8, 0.4, 0.2];  % 橙色 - CLKS域
        end
    end
    
    % 子图1：平均绝对误差
    subplot(1, 2, 1);
    bar_handle = bar(stage_mae);
    bar_handle.FaceColor = 'flat';
    bar_handle.CData = colors;
    
    set(gca, 'XTickLabel', stage_labels, 'XTickLabelRotation', 45);
    ylabel('平均绝对误差 (V)', 'FontSize', 12);
    title('各级平均绝对误差对比 (MAE = mean(|sim - ideal|))', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    
    % 子图2：最大绝对误差
    subplot(1, 2, 2);
    bar_handle = bar(stage_max_error);
    bar_handle.FaceColor = 'flat';
    bar_handle.CData = colors;
    
    set(gca, 'XTickLabel', stage_labels, 'XTickLabelRotation', 45);
    ylabel('最大绝对误差 (V)', 'FontSize', 12);
    title('各级最大绝对误差对比 (Max = max(|sim - ideal|))', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    
    % 添加图例 - 使用两个虚拟数据点来确保两种颜色都显示
    hold on;
    h1 = bar(NaN, NaN, 'FaceColor', [0.2, 0.6, 0.8]);
    h2 = bar(NaN, NaN, 'FaceColor', [0.8, 0.4, 0.2]);
    legend([h1, h2], {'CLKH域', 'CLKS域'}, 'Location', 'best');
    hold off;
    
    % 添加总标题
    sgtitle('流水线ADC误差统计对比 (误差 = 晶体管仿真值 - 理想模型值)', 'FontSize', 16, 'FontWeight', 'bold');
    
end

function save_comparison_data(extracted_data)
%SAVE_COMPARISON_DATA 保存对比数据到文件
%   将提取的数据和详细的误差数据保存为.mat文件

    % 创建保存目录
    save_dir = './test_data';
    if ~exist(save_dir, 'dir')
        mkdir(save_dir);
    end
    
    % 生成带时间戳的文件名
    filename = sprintf('pipeline_comparison_data.mat');
    filepath = fullfile(save_dir, filename);
    
    % 准备保存的数据结构
    comparison_data = struct();
    comparison_data.save_info = struct();
    comparison_data.save_info.timestamp = datetime("now", 'Format', 'yyyy-MM-dd_HH-mm-ss');
    
    % 保存提取的原始数据
    comparison_data.extracted_data = extracted_data;
    
    % 计算并保存详细的误差数据
    stage_names = {'SHA', 'STAGE1', 'STAGE2', 'STAGE3', 'STAGE4', ...
                   'STAGE5', 'STAGE6', 'STAGE7', 'STAGE8'};
    
    comparison_data.error_summary = struct();
    comparison_data.error_data = struct();
    
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        
        if ~isfield(extracted_data, stage_name)
            continue;
        end
        
        stage_data = extracted_data.(stage_name);
        ideal_signal = stage_data.ideal_signal;
        sim_signal = stage_data.sim_signal;
        
        % 计算每个样本点的误差
        error_signal = sim_signal - ideal_signal;
        
        % 保存详细的误差数据（每个样本点的误差）
        detailed_error = struct();
        detailed_error.sample_error = error_signal;                    % 每个样本点的误差值
        detailed_error.absolute_error = abs(error_signal);             % 每个样本点的绝对误差值
        detailed_error.sample_indices = (1:length(error_signal))';     % 样本序号
        detailed_error.ideal_values = ideal_signal;                    % 理想值
        detailed_error.sim_values = sim_signal;                        % 仿真值
        
        comparison_data.error_data.(stage_name) = detailed_error;
        
        % 计算统计误差信息
        error_stats = struct();
        error_stats.mean_absolute_error = mean(abs(error_signal));
        error_stats.max_absolute_error = max(abs(error_signal));
        error_stats.sample_count = length(error_signal);
        
        comparison_data.error_summary.(stage_name) = error_stats;
    end
    
    % 添加误差数据的全局统计信息
    comparison_data.global_error_stats = struct();
    comparison_data.global_error_stats.description = '各级误差数据的全局统计信息';
    comparison_data.global_error_stats.total_samples = sum(arrayfun(@(x) x.sample_count, ...
                                                           struct2array(comparison_data.error_summary)));
    
    % 计算所有级别的误差汇总
    all_errors = [];
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        if isfield(comparison_data.error_data, stage_name)
            all_errors = [all_errors; comparison_data.error_data.(stage_name).sample_error];
        end
    end
    
    if ~isempty(all_errors)
        comparison_data.global_error_stats.overall_mae = mean(abs(all_errors));
        comparison_data.global_error_stats.overall_max_error = max(abs(all_errors));
        comparison_data.global_error_stats.overall_min_error = min(abs(all_errors));
        comparison_data.global_error_stats.overall_rms_error = sqrt(mean(all_errors.^2));
        comparison_data.global_error_stats.overall_std_error = std(all_errors);
    end
    
    % 保存文件
    save(filepath, 'comparison_data');
    
    % 获取文件信息
    file_info = dir(filepath);
    file_size_kb = file_info.bytes / 1024;
    
    fprintf('  保存文件: %s\n', filename);
    fprintf('  文件大小: %.2f KB\n', file_size_kb);
    fprintf('  保存路径: %s\n', filepath);
end 