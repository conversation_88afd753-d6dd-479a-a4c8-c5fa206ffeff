function [dnl, inl, static_metrics] = calculate_static_performance(input_signal, digital_output, params)
%CALCULATE_STATIC_PERFORMANCE 静态特性计算函数
%   版本历史:
%   v1.0 - 实现基于码密度法的DNL/INL测量功能
%
%   使用码密度法和MATLAB内置inldnl()函数计算ADC的差分非线性(DNL)
%   和积分非线性(INL)，包含数据对齐、延迟补偿和格式转换
%
%   输入参数:
%       input_signal - 对齐后的输入信号向量（斜坡信号）
%       digital_output - ADC数字输出矩阵，10位二进制码
%       params - ADC参数结构体
%           fs - 采样频率 (Hz)
%           resolution - ADC分辨率 (bit)
%
%   输出参数:
%       dnl - 差分非线性数组 (LSB)
%       inl - 积分非线性数组 (LSB)  
%       static_metrics - 静态特性统计信息结构体

    fprintf('=== 静态特性计算 ===\n');
    
    %% 参数验证
    if nargin < 3
        error('需要提供input_signal, digital_output和params参数');
    end
    
    % 获取ADC参数
    fs = params.fs;
    N_bits = params.resolution;
    
    fprintf('ADC参数: %d位分辨率, 采样频率%.1f MHz\n', N_bits, fs/1e6);
    
    %% 数据长度验证
    input_length = length(input_signal);
    [output_length, num_bits] = size(digital_output);
    
    if num_bits ~= N_bits
        error('数字输出位数不匹配: 期望%d位, 实际%d位', N_bits, num_bits);
    end
    
    fprintf('数据验证: 输入%d点, 输出%d点x%d位\n', input_length, output_length, num_bits);
    
    %% 流水线延迟补偿和数据对齐
    samples_per_cycle = 1000;  % 每个采样周期的时间点数（与generate_timebase.m一致）
    pipeline_delay_cycles = 4;  % 流水线延迟4个采样周期
    pipeline_delay = pipeline_delay_cycles * samples_per_cycle;  % 总延迟时间点数：4000
    
    [aligned_input, aligned_output] = pipeline_delay_compensation(...
        input_signal, digital_output, pipeline_delay, pipeline_delay_cycles);
    
    aligned_length = length(aligned_input);
    fprintf('延迟补偿完成: %d个采样周期(%d时间点), 对齐后数据长度%d点\n', ...
            pipeline_delay_cycles, pipeline_delay, aligned_length);
    
    %% 数字码权重赋值和格式转换
    weighted_codes = convert_binary_to_weighted_codes(aligned_output, N_bits);
    
    % 验证权重码范围
    code_min = min(weighted_codes);
    code_max = max(weighted_codes);
    expected_min = -2^(N_bits-1);
    expected_max = 2^(N_bits-1) - 1;
    
    fprintf('权重码范围: [%d, %d], 期望范围: [%d, %d]\n', ...
            code_min, code_max, expected_min, expected_max);
    
    %% 调用MATLAB内置inldnl()函数计算DNL/INL
    try
        [dnl, inl] = inldnl(weighted_codes, N_bits);
        fprintf('✓ MATLAB inldnl()函数调用成功\n');
    catch ME
        error('inldnl()函数调用失败: %s', ME.message);
    end
    
    %% 计算静态特性统计信息
    static_metrics = calculate_static_metrics(dnl, inl, aligned_input, weighted_codes, params);
    
    fprintf('静态特性计算完成:\n');
    fprintf('  DNL: RMS=%.3f LSB, Max=%.3f LSB\n', static_metrics.rms_dnl, static_metrics.max_dnl);
    fprintf('  INL: RMS=%.3f LSB, Max=%.3f LSB\n', static_metrics.rms_inl, static_metrics.max_inl);
end

function [aligned_input, aligned_output] = pipeline_delay_compensation(input_signal, digital_output, pipeline_delay, pipeline_delay_cycles)
%PIPELINE_DELAY_COMPENSATION 流水线延迟补偿和数据对齐
%   根据流水线ADC的固定延迟进行输入输出数据对齐
%   确保输入信号样本点与数字输出码一一对应
%
%   输入参数:
%       input_signal - 原始输入信号向量
%       digital_output - 原始数字输出矩阵
%       pipeline_delay - 流水线延迟（时间点数）
%       pipeline_delay_cycles - 流水线延迟（采样周期数）
%
%   输出参数:
%       aligned_input - 对齐后的输入信号
%       aligned_output - 对齐后的数字输出

    input_length = length(input_signal);
    output_length = size(digital_output, 1);
    
    % 确定有效数据长度
    valid_length = min(input_length, output_length) - pipeline_delay;
    
    if valid_length <= 0
        error('延迟补偿失败: 数据长度不足，需要延迟%d时间点(%d周期)', pipeline_delay, pipeline_delay_cycles);
    end
    
    % 数据对齐：输入信号取前valid_length个点，输出取延迟后的valid_length个点
    aligned_input = input_signal(1:valid_length);
    aligned_output = digital_output(pipeline_delay+1:pipeline_delay+valid_length, :);
    
    fprintf('数据对齐: 延迟%d周期, 有效长度%d点\n', pipeline_delay, valid_length);
end

function weighted_codes = convert_binary_to_weighted_codes(binary_output, N_bits)
%CONVERT_BINARY_TO_WEIGHTED_CODES 二进制码转权重码
%   将10位二进制码转换为权重赋值后的有符号整数码
%   使用与digital_correction_v4.m相同的权重计算逻辑
%   范围为[-512, 511]，用于INL/DNL计算
%
%   输入参数:
%       binary_output - 二进制输出矩阵 (samples x N_bits)
%       N_bits - ADC分辨率位数
%
%   输出参数:
%       weighted_codes - 权重码向量

    [~, num_bits] = size(binary_output);
    
    if num_bits ~= N_bits
        error('位数不匹配: 输入%d位, 期望%d位', num_bits, N_bits);
    end
    
    % 使用与digital_correction_v4.m相同的权重向量
    % DAC权重：[512, 256, 128, 64, 32, 16, 8, 4, 2, 1]
    weights = 2.^(N_bits-1:-1:0);  % [2^9, 2^8, ..., 2^1, 2^0]
    
    % 权重累加：计算加权和 [0, 1023]
    weighted_sums = binary_output * weights';
    
    % 转换为有符号权重码：[-512, 511]
    % 这里减去512，与digital_correction_v4.m的逻辑一致
    weighted_codes = weighted_sums - 512;
    
    fprintf('码值转换: 二进制→权重码, 范围[%d, %d]\n', ...
            min(weighted_codes), max(weighted_codes));
    
end

function static_metrics = calculate_static_metrics(dnl, inl, input_signal, weighted_codes, params)
%CALCULATE_STATIC_METRICS 计算静态特性统计信息
%   计算DNL/INL的统计指标和相关信息
%
%   输入参数:
%       dnl - 差分非线性数组
%       inl - 积分非线性数组
%       input_signal - 输入信号
%       weighted_codes - 权重码
%       params - ADC参数
%
%   输出参数:
%       static_metrics - 静态特性统计信息结构体

    static_metrics = struct();
    
    % DNL统计
    static_metrics.rms_dnl = rms(dnl);
    static_metrics.max_dnl = max(abs(dnl));
    static_metrics.min_dnl = min(dnl);
    static_metrics.max_pos_dnl = max(dnl);
    static_metrics.max_neg_dnl = min(dnl);
    
    % INL统计
    static_metrics.rms_inl = rms(inl);
    static_metrics.max_inl = max(abs(inl));
    static_metrics.min_inl = min(inl);
    static_metrics.max_pos_inl = max(inl);
    static_metrics.max_neg_inl = min(inl);
    
    % 数据统计
    static_metrics.num_samples = length(input_signal);
    static_metrics.code_range = [min(weighted_codes), max(weighted_codes)];
    static_metrics.input_range = [min(input_signal), max(input_signal)];
    
    % 测试参数
    static_metrics.adc_resolution = params.resolution;
    static_metrics.sampling_frequency = params.fs;
    
    % 测试时间戳
    static_metrics.test_timestamp = datetime('now');
    
end 