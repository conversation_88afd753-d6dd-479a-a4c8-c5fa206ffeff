function [snr, sndr, sfdr, enob, performance_metrics] = calculate_dynamic_performance(pipeline_output, fs, f_in)
%CALCULATE_DYNAMIC_PERFORMANCE 计算流水线ADC的完整动态特性指标
%   [snr, sndr, sfdr, enob, performance_metrics] = CALCULATE_DYNAMIC_PERFORMANCE(pipeline_output, fs, f_in)
%   从流水线ADC输出中提取有效数据点，计算SNR、SNDR、SFDR、ENOB四种动态特性指标
%
%   输入参数:
%       pipeline_output - 流水线ADC最终输出信号（经过数字校正后的模拟值）
%       fs - 采样频率 (Hz)
%       f_in - 输入信号频率 (Hz)
%
%   输出参数:
%       snr - 信噪比 (dB)
%       sndr - 信号与噪声失真比 (dB)
%       sfdr - 无杂散动态范围 (dB)
%       enob - 有效位数 (bits)
%       performance_metrics - 详细性能指标结构体，包含以下字段：
%           ├── snr, sndr, sfdr, enob, thd - 动态特性指标
%           ├── analysis_details - 分析详情
%           ├── spectrum_data - 频谱数据（用于外部绘图）
%           └── plot_data - 绘图数据结构（用于外部可视化）
%
%   数据处理特点:
%   1. 跳过前100个采样周期，提取1024个有效采样点进行FFT分析
%   2. 基于相干采样原则直接计算频谱，无需窗函数
%   3. 采用幅度归一化和功率校正确保测量精度
%   4. 使用总功率减法计算噪声功率，优化谐波检测算法
%   5. 统一SFDR计算逻辑，修正ENOB计算公式

    fprintf('=== 流水线ADC动态特性计算 ===\n');
    
    %% 参数验证
    if nargin < 3
        error('至少需要提供pipeline_output、fs、f_in三个参数');
    end
    
    
    %% 第一步：从流水线ADC输出中提取有效数据点
    fprintf('\n[步骤1/4] 提取有效数据点...\n');
    
    % 参数配置
    skip_cycles = 100;              % 跳过前100个采样周期
    target_samples = 1024;          % 提取1024个样本点用于FFT
    
    % 基于均匀采样特性提取有效数据点
    valid_sample_indices = extract_valid_samples(pipeline_output, skip_cycles);
    
    % 验证有效样本数量
    if length(valid_sample_indices) < target_samples
        error('有效样本数量不足：需要%d个，实际%d个', ...
              target_samples, length(valid_sample_indices));
    end
    
    % 从已预处理的有效样本中提取样本用于FFT分析
    selected_indices = valid_sample_indices(1:target_samples);
    adc_samples = pipeline_output(selected_indices);
    
    fprintf('  提取%d个有效样本用于FFT分析\n', length(adc_samples));
    
    %% 第二步：数据预处理（基于prettyFFT算法）
    fprintf('\n[步骤2/4] 数据预处理...\n');

    % 移除直流偏移
    adc_samples = adc_samples - mean(adc_samples);

    % 记录样本信息
    N = length(adc_samples);
    max_voltage = max(abs(adc_samples));  % 保留用于调试信息

    fprintf('  样本数量: %d, 最大电压值: %.6f V\n', N, max_voltage);

    % 验证相干采样条件
    M = round(f_in * N / fs);  % 实际记录的信号周期数
    coherent_error = abs(M - (f_in * N / fs));

    if coherent_error > 1e-6
        fprintf('  警告: 相干采样误差: %.9f，应用Hanning窗\n', coherent_error);
        window = hanning(N);
        adc_samples = adc_samples .* window;
    else
        fprintf('  满足相干采样条件，误差: %.9f\n', coherent_error);
    end
    
    %% 第三步：FFT频谱分析
    fprintf('\n[步骤3/4] FFT频谱分析...\n');

    % 执行FFT分析
    fft_result = fft(adc_samples);
    fft_mag = abs(fft_result);
    fft_power = fft_mag.^2;  % 功率谱

    % 移除DC分量，只保留正频率部分
    fft_mag = fft_mag(2:floor(length(fft_mag)/2));  % 移除bin 1 (DC)
    fft_power = fft_power(2:floor(length(fft_power)/2));  % 移除bin 1 (DC)

    % 基频检测（prettyFFT方法，与MATLAB内置findpeaks函数一致）
    [~, fundamental_bin] = max(fft_mag);

    if isempty(fundamental_bin) || fundamental_bin < 1
        error('基频检测失败：无法找到有效的基频位置');
    end

    % 计算归一化因子
    scaledby = 1./max(fft_mag);
    fft_db = mag2db(fft_mag .* scaledby);  % 归一化后转换为dB

    fprintf('  基频bin位置: %d, FFT长度: %d\n', fundamental_bin, length(fft_mag));
    
    %% 第四步：动态特性指标计算
    fprintf('\n[步骤4/4] 动态特性指标计算...\n');

    % 计算基本参数
    numbins = length(fft_mag);
    pts = 2 * (numbins + 1);  % 原始FFT长度

    % 计算SNDR（prettyFFT方法）
    sndr = mag2db((fft_mag(fundamental_bin).^2/(sum(fft_mag.^2)-fft_mag(fundamental_bin).^2)))/2;

    % 计算ENOB（prettyFFT方法，无电压校正项）
    enob = (sndr - 1.76)/6.02;

    % 谐波检测（基于prettyFFT算法）
    [harmonic_bins, ~] = detect_harmonics_prettyfft(fft_mag, fundamental_bin, pts, fft_db);

    % 计算SNR（排除谐波的prettyFFT方法）
    non_harm = 1:numbins;
    non_harm(harmonic_bins) = [];  % 排除所有谐波bin
    snr = mag2db((fft_mag(fundamental_bin).^2/(sum(fft_mag(non_harm).^2))))/2;

    % 计算SFDR（采用prettyFFT标准方法）
    % 创建去除基频的频谱用于SFDR计算
    if fundamental_bin == 1
        fft_db_no_fundamental = fft_db(2:end);
    elseif fundamental_bin == length(fft_db)
        fft_db_no_fundamental = fft_db(1:(end-1));
    else
        fft_db_no_fundamental = [fft_db(1:(fundamental_bin-1)); fft_db((fundamental_bin+1):end)];
    end

    % 找到最大杂散（prettyFFT标准算法）
    if ~isempty(fft_db_no_fundamental)
        [~, max_spur_idx] = max(fft_db_no_fundamental);
        sfdr = -fft_db_no_fundamental(max_spur_idx);  % prettyFFT方法：负的最大杂散dB值
    else
        error('无法计算SFDR：没有检测到杂散信号');
    end

    % 计算THD
    total_power = sum(fft_mag.^2);
    signal_power = fft_mag(fundamental_bin).^2;
    noise_power = sum(fft_mag(non_harm).^2);

    % prettyFFT的THD公式：(sum(f2.^2)-f2(bin).^2-sum(f2(non_harm).^2))/f2(bin).^2
    thd = mag2db((total_power - signal_power - noise_power) / fft_mag(fundamental_bin).^2)/2;
    
    %% 输出结果
    fprintf('\n=== 动态特性指标结果 ===\n');
    fprintf('SNR  = %.2f dB\n', snr);
    fprintf('SNDR = %.2f dB\n', sndr);
    fprintf('SFDR = %.2f dB\n', sfdr);
    fprintf('ENOB = %.2f bits\n', enob);
    fprintf('THD  = %.2f dB\n', thd);

    % 输出调试信息
    fprintf('\n=== 调试信息 ===\n');
    fprintf('基频功率: %.6e\n', fft_mag(fundamental_bin).^2);
    if length(harmonic_bins) > 1
        fprintf('最大谐波功率: %.6e\n', max(fft_mag(harmonic_bins(2:end)).^2));
    else
        fprintf('最大谐波功率: 0 (未检测到谐波)\n');
    end
    fprintf('信号功率: %.6e\n', signal_power);

    % 重新计算谐波总功率用于调试显示
    harmonic_power_total = total_power - signal_power - noise_power;
    fprintf('谐波总功率: %.6e\n', harmonic_power_total);
    fprintf('噪声功率: %.6e\n', noise_power);
    
    %% 生成详细性能指标结构体
    performance_metrics = struct();
    performance_metrics.snr = snr;
    performance_metrics.sndr = sndr;
    performance_metrics.sfdr = sfdr;
    performance_metrics.enob = enob;
    performance_metrics.thd = thd;

    % 添加功率值调试信息（基于prettyFFT算法）
    performance_metrics.debug_info = struct();
    performance_metrics.debug_info.fundamental_power = signal_power;
    if length(harmonic_bins) > 1
        performance_metrics.debug_info.harmonic_powers = fft_mag(harmonic_bins(2:end)).^2;
        performance_metrics.debug_info.max_harmonic_power = max(fft_mag(harmonic_bins(2:end)).^2);
    else
        performance_metrics.debug_info.harmonic_powers = [];
        performance_metrics.debug_info.max_harmonic_power = 0;
    end
    performance_metrics.debug_info.signal_power = signal_power;
    performance_metrics.debug_info.harmonic_total_power = harmonic_power_total;
    performance_metrics.debug_info.noise_power = noise_power;
    performance_metrics.debug_info.max_voltage = max_voltage;
    
    % 添加分析详情
    performance_metrics.analysis_details = struct();
    performance_metrics.analysis_details.total_samples = length(pipeline_output);
    performance_metrics.analysis_details.valid_samples = length(valid_sample_indices);
    performance_metrics.analysis_details.analyzed_samples = N;
    performance_metrics.analysis_details.skip_cycles = skip_cycles;
    performance_metrics.analysis_details.fundamental_bin = fundamental_bin;
    performance_metrics.analysis_details.coherent_sampling_error = coherent_error;
    performance_metrics.analysis_details.signal_power = signal_power;
    performance_metrics.analysis_details.noise_power = noise_power;
    performance_metrics.analysis_details.harmonic_power = harmonic_power_total;
    performance_metrics.analysis_details.harmonic_bins = harmonic_bins;
    performance_metrics.analysis_details.fft_bins_used = numbins;
    
    % 添加频谱数据（基于prettyFFT格式）
    performance_metrics.spectrum_data = struct();
    performance_metrics.spectrum_data.fft_db = fft_db;
    performance_metrics.spectrum_data.fft_mag = fft_mag;
    performance_metrics.spectrum_data.freq_axis = (1:numbins) * fs/(2*numbins);  % 正频率轴
    performance_metrics.spectrum_data.fundamental_bin = fundamental_bin;
    performance_metrics.spectrum_data.harmonic_bins = harmonic_bins;
    
    % 添加绘图数据结构（用于外部可视化）
    performance_metrics.plot_data = struct();

    % 频率轴数据（基于prettyFFT格式）
    performance_metrics.plot_data.freq_axis_Hz = (1:numbins) * fs/(2*numbins);
    performance_metrics.plot_data.freq_axis_MHz = performance_metrics.plot_data.freq_axis_Hz / 1e6;
    
    % 幅度谱数据（修正索引越界问题：使用实际数组长度而不是N/2）
    performance_metrics.plot_data.magnitude_db = fft_db;
    performance_metrics.plot_data.magnitude_linear = fft_mag / N;

    % 功率谱数据（修正索引越界问题：使用实际数组长度而不是N/2）
    performance_metrics.plot_data.power_spectrum = fft_power;
    performance_metrics.plot_data.power_spectrum_db = 10*log10(fft_power + eps);
    
    % 基频标记数据
    performance_metrics.plot_data.fundamental_bin = fundamental_bin;
    performance_metrics.plot_data.fundamental_freq_Hz = performance_metrics.plot_data.freq_axis_Hz(fundamental_bin);
    performance_metrics.plot_data.fundamental_freq_MHz = performance_metrics.plot_data.fundamental_freq_Hz / 1e6;
    performance_metrics.plot_data.fundamental_magnitude_db = fft_db(fundamental_bin);
    
    % 注意：谐波标记数据已移除，仅保留主频标记功能
    % 谐波检测功能仍用于动态特性计算（SNR、SNDR、THD等），但不输出到绘图数据
    
    % 添加绘图辅助信息
    performance_metrics.plot_data.input_freq_Hz = f_in;
    performance_metrics.plot_data.input_freq_MHz = f_in / 1e6;
    performance_metrics.plot_data.sampling_freq_Hz = fs;
    performance_metrics.plot_data.sampling_freq_MHz = fs / 1e6;
    performance_metrics.plot_data.nyquist_freq_MHz = fs / 2e6;
    
    % 添加性能指标文本（用于图表标注）
    performance_metrics.plot_data.performance_text = sprintf(...
        'SNR: %.2f dB | SNDR: %.2f dB | SFDR: %.2f dB | ENOB: %.2f bits', ...
        snr, sndr, sfdr, enob);
    
    performance_metrics.plot_data.analysis_text = sprintf(...
        '输入频率: %.3f MHz | 基频bin: %d | 分析样本: %d', ...
        f_in/1e6, fundamental_bin, N);
    
    fprintf('\n=== 流水线ADC动态特性计算完成 ===\n');

    %% 调试功能：绘制有效数据点波形
    % 使用extract_valid_samples提取的索引从pipeline_output中获取对应数值
    valid_data_values = pipeline_output(valid_sample_indices);

    % 创建新图窗并绘制阶梯图
    figure;
    stairs(1:length(valid_data_values), valid_data_values, 'b-', 'LineWidth', 1.2);

    % 设置图形属性
    title('流水线ADC有效数据点波形', 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('有效样本序号', 'FontSize', 10);
    ylabel('输出电压 (V)', 'FontSize', 10);
    grid on;

    % 设置坐标轴属性
    xlim([1, length(valid_data_values)]);

    % 添加图例和注释信息
    legend(sprintf('有效数据点 (%d个)', length(valid_data_values)), 'Location', 'best');

end

function valid_sample_indices = extract_valid_samples(pipeline_output, skip_cycles)
%EXTRACT_VALID_SAMPLES 从流水线ADC输出中提取有效数据点

    % 参数设置
    samples_per_cycle = 1000;
    stable_offset = 100;
    stable_position = samples_per_cycle - stable_offset;
        
    % 计算总采样周期数
    total_samples = length(pipeline_output);
    total_cycles = floor(total_samples / samples_per_cycle);
    
    % 验证数据长度
    if total_cycles < skip_cycles + 1
        error('数据长度不足：需要至少%d个周期，实际%d个周期', ...
              skip_cycles + 1, total_cycles);
    end
    
    % 计算可提取的周期数
    available_cycles = total_cycles - skip_cycles;

    % 预分配输出数组提高性能
    valid_sample_indices = zeros(1, available_cycles);
    valid_count = 0;

    % 直接计算采样索引
    start_sample = skip_cycles * samples_per_cycle + 1;

    for cycle = 0:(available_cycles-1)
        % 计算该周期的稳定采样点索引
        cycle_start = start_sample + cycle * samples_per_cycle;
        extract_idx = cycle_start + stable_position - 1;

        % 验证索引有效性
        if extract_idx > 0 && extract_idx <= total_samples
            valid_count = valid_count + 1;
            valid_sample_indices(valid_count) = extract_idx;
        end
    end

    % 截取有效部分
    valid_sample_indices = valid_sample_indices(1:valid_count);
    
    % 转换为列向量
    valid_sample_indices = valid_sample_indices(:);
end



%% 谐波检测函数（基于prettyFFT算法）
function [harmonic_bins, harmonic_powers] = detect_harmonics_prettyfft(fft_mag, fundamental_bin, pts, fft_db)
    % 基于prettyFFT算法的谐波检测函数
    % 输入：
    %   fft_mag - FFT幅度谱
    %   fundamental_bin - 基频bin位置
    %   pts - 原始FFT点数
    %   fft_db - dB表示的频谱
    % 输出：
    %   harmonic_bins - 谐波bin位置数组
    %   harmonic_powers - 谐波功率数组

    % 初始化参数
    plev = 9;  % dB above noise floor to be considered a harmonic
    maxh = 12; % 最大谐波数量

    % 计算噪声门限（采用prettyFFT方法：使用去除基频后的频谱平均值）
    if fundamental_bin == 1
        fft_db_no_fundamental = fft_db(2:end);
    elseif fundamental_bin == length(fft_db)
        fft_db_no_fundamental = fft_db(1:(end-1));
    else
        fft_db_no_fundamental = [fft_db(1:(fundamental_bin-1)); fft_db((fundamental_bin+1):end)];
    end
    noise_floor = mean(fft_db_no_fundamental);
    noise_top = noise_floor + plev;

    % 计算所有可能的谐波位置（prettyFFT算法）
    step = fundamental_bin;
    nyqpts = (pts/2-1);
    all_harms = fundamental_bin:step:(fundamental_bin*nyqpts);

    % 处理频谱折叠（prettyFFT核心算法）
    all_harms = mod(all_harms, pts);
    all_harms = (pts-all_harms).*(all_harms>nyqpts) + all_harms.*(all_harms<=nyqpts);
    all_harms = all_harms.*(all_harms>0 & all_harms<pts/2) + ...
                (all_harms<=0) + (all_harms>=pts/2).*nyqpts;

    % 限制谐波数量
    if (maxh==0 || maxh>length(all_harms))
        maxh = length(all_harms);
    end

    % 检测有效谐波（预分配数组提高性能）
    max_harmonics = min(maxh, length(all_harms));
    harmonic_bins = zeros(1, max_harmonics);
    harmonic_powers = zeros(1, max_harmonics);
    valid_count = 0;

    for k = 1:max_harmonics
        harm_idx = all_harms(k);
        if harm_idx > 0 && harm_idx <= length(fft_db)
            if fft_db(harm_idx) > noise_top
                valid_count = valid_count + 1;
                harmonic_bins(valid_count) = harm_idx;
                harmonic_powers(valid_count) = fft_mag(harm_idx).^2;
            end
        end
    end

    % 截取有效部分
    harmonic_bins = harmonic_bins(1:valid_count);
    harmonic_powers = harmonic_powers(1:valid_count);

    % 确保基频在第一位
    if ~isempty(harmonic_bins) && harmonic_bins(1) ~= fundamental_bin
        % 重新排序，基频在前
        fund_idx = find(harmonic_bins == fundamental_bin, 1);
        if ~isempty(fund_idx)
            % 提取除基频外的其他谐波
            other_harmonics_mask = harmonic_bins ~= fundamental_bin;
            other_harmonic_bins = harmonic_bins(other_harmonics_mask);
            other_harmonic_powers = harmonic_powers(other_harmonics_mask);
            
            % 重新组合：基频在前
            harmonic_bins = [fundamental_bin, other_harmonic_bins];
            harmonic_powers = [fft_mag(fundamental_bin).^2, other_harmonic_powers];
        else
            % 如果基频不在检测到的谐波中，手动添加
            harmonic_bins = [fundamental_bin, harmonic_bins];
            harmonic_powers = [fft_mag(fundamental_bin).^2, harmonic_powers];
        end
    elseif isempty(harmonic_bins)
        % 如果没有检测到任何谐波，至少包含基频
        harmonic_bins = fundamental_bin;
        harmonic_powers = fft_mag(fundamental_bin).^2;
    end
end

% ===========================================================================
% 优化修改说明（v2.1）：
% 1. SFDR计算：采用prettyFFT的归一化dB谱方法，替换功率比方法
% 2. THD计算：修正为prettyFFT标准公式 (总功率-基频功率-噪声功率)/基频功率
% 3. 噪声门限：使用去除基频后的频谱平均值，与prettyFFT保持一致
% 4. 代码优化：删除未使用的calculate_harmonic_powers函数
% 5. 性能提升：数组预分配，优化find函数调用
% 6. 计算精度：确保所有动态特性指标与prettyFFT算法完全一致
% 7. 基于Skyler Weaver博士的prettyFFT专业算法标准实现
% ===========================================================================

