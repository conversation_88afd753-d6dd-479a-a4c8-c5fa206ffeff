%% 流水线ADC模型完整功能验证主脚本
% 验证模型是否完全符合用户设计思路与要求
% 重点：理想状态、两种模拟输出、精确时序控制
% VERSION: v7.2 - 精简优化版本（注重静态特性测试）
% 核心功能：时间基准生成 → 事件驱动ADC处理 → 静态特性测试 → 结果可视化

clear; clc; close all;

fprintf('=== 流水线ADC模型完整功能验证（静态特性测试） ===\n');

%% 设置测试参数
params = struct();
params.Vref = 1.0;                % 参考电压
params.num_stages = 8;            % 8级流水线
params.fs = 100e6;               % 100MHz采样频率
params.resolution = 10;           % 10位分辨率
params.Vcm = 0;                   % 共模电压
params.f_in = 23/1024*100e6;            % 输入正弦波频率 (符合相干采样要求)

fprintf('ADC参数设置完成:\n');
fprintf('  流水线级数: %d级\n', params.num_stages);
fprintf('  采样频率: %.1f MHz\n', params.fs/1e6);
fprintf('  输入频率: %.3f MHz\n', params.f_in/1e6);
fprintf('  分辨率: %d bit\n', params.resolution);

%% 设置误差参数矩阵（零误差理想验证）
% 8×3矩阵格式：[gain_error, cap_mismatch, offset_error]
rel_error_params = zeros(params.num_stages, 3);

fprintf('\n误差参数设置完成 (8×3零误差矩阵)\n');

%% 设置测试类型（可根据需求调整）
test_config = struct();
test_config.run_dynamic_test = true;   % 动态性能测试（使用正弦波）
test_config.run_static_test = false;    % 静态特性测试（使用斜坡信号）

%% 步骤1: 动态性能测试（正弦波信号）
dynamic_test_success = false;
if test_config.run_dynamic_test
    fprintf('\n[步骤1/2] 动态性能测试（正弦波信号）...\n');
    
    try
        % 设置正弦波参数并生成信号
        params_sine = params;
        params_sine.signal_type = 'sine';  % 明确设置为正弦波
        
        % 固定步长信号生成（替换为可变步长版本）
        % [t_sine, sine_signal, clks_sine, clkh_sine, ~, ~] = generate_timebase(params_sine);
        
        % 使用可变步长时间索引信号生成
        [t_sine, sine_signal, clks_sine, clkh_sine, ~, ~] = generate_timebase_variable_step(params_sine);
        
        if isempty(t_sine) || isempty(sine_signal) || isempty(clks_sine) || isempty(clkh_sine)
            error('正弦波信号生成失败：未能生成有效的输入波形或时钟信号');
        end
        
        % 验证非交叠时钟
        if any((clks_sine == 1) & (clkh_sine == 1))
            error('非交叠时钟验证失败：检测到时钟重叠');
        end
        
        fprintf('✓ 正弦波信号生成成功: %d样本, 非交叠时钟验证通过\n', length(t_sine));
        
        % 创建差分正弦输入信号用于动态测试
        Vin_p_sine = sine_signal*1/2;
        Vin_n_sine = -sine_signal*1/2;
        
        fprintf('✓ 正弦信号准备完成，信号幅度: ±%.3f V\n', max(abs(sine_signal))/2);
        
        % 运行流水线ADC处理正弦信号
        fprintf('运行流水线ADC处理正弦信号...\n');
        [sine_correction_output, sine_processing_info, sine_correction_metrics, sine_raw_analog_data] = ...
            event_driven_pipeline_adc_v4(Vin_p_sine, Vin_n_sine, params_sine, rel_error_params);
        
        % 提取ADC模拟输出用于动态性能分析
        if ~isempty(sine_correction_output.signals) && length(sine_correction_output.signals) >= 1
            pipeline_output = sine_correction_output.signals(1).values;  % 第一个信号是模拟输出
            
            % 提取数字输出码用于可视化
            sine_binary_output = zeros(length(sine_correction_output.signals(2).values), 10);
            for bit_idx = 1:10
                sine_binary_output(:, bit_idx) = sine_correction_output.signals(bit_idx + 1).values;
            end
            
            % 计算动态性能指标
            fprintf('计算动态性能指标...\n');
            [snr, sndr, sfdr, enob, performance_metrics] = calculate_dynamic_performance(...
                pipeline_output, params_sine.fs, params_sine.f_in, t_sine);
            
            % 创建动态测试数据结构体
            dynamic_test_data = create_dynamic_test_data_struct(...
                snr, sndr, sfdr, enob, performance_metrics, ...
                pipeline_output, clkh_sine, clks_sine, sine_binary_output, sine_raw_analog_data);
            
            fprintf('✓ 动态性能测试完成: SNR=%.1f dB, SNDR=%.1f dB, ENOB=%.1f bits\n', ...
                    snr, sndr, enob);
            dynamic_test_success = true;
            
        else
            error('正弦信号ADC输出结构体无效');
        end
        
    catch ME
        fprintf('✗ 动态性能测试准备失败: %s\n', ME.message);
        dynamic_test_success = false;
    end
else
    fprintf('\n[步骤1/2] 跳过动态性能测试（根据配置）\n');
end

%% 步骤2: 静态特性测试（斜坡信号）
static_test_success = false;
if test_config.run_static_test
    fprintf('\n[步骤2/2] 静态特性测试（斜坡信号）...\n');
    
    try
        % 设置斜坡信号参数并生成信号
        params_ramp = params;
        params_ramp.signal_type = 'ramp';  % 设置为斜坡信号
        
        % 固定步长信号生成（替换为可变步长版本）
        % [t_ramp, ramp_signal, clks_ramp, clkh_ramp, ~, ~] = generate_timebase(params_ramp);
        
        % 使用可变步长时间索引信号生成
        [t_ramp, ramp_signal, clks_ramp, clkh_ramp, ~, ~] = generate_timebase_variable_step(params_ramp);
        
        if isempty(t_ramp) || isempty(ramp_signal) || isempty(clks_ramp) || isempty(clkh_ramp)
            error('斜坡信号生成失败：未能生成有效的输入波形或时钟信号');
        end
        
        % 验证非交叠时钟
        if any((clks_ramp == 1) & (clkh_ramp == 1))
            error('非交叠时钟验证失败：检测到时钟重叠');
        end
        
        fprintf('✓ 斜坡信号生成成功: %d样本, 非交叠时钟验证通过\n', length(t_ramp));
        
        % 创建差分斜坡输入信号
        Vin_p_ramp = ramp_signal*1/2;
        Vin_n_ramp = -ramp_signal*1/2;
        
        % 运行流水线ADC处理斜坡信号
        [ramp_correction_output, ramp_processing_info, ramp_correction_metrics, ~] = ...
            event_driven_pipeline_adc_v4(Vin_p_ramp, Vin_n_ramp, params_ramp, rel_error_params);
        
        % 提取数字输出码用于静态分析
        if ~isempty(ramp_correction_output.signals) && length(ramp_correction_output.signals) >= 11
            ramp_binary_output = zeros(length(ramp_correction_output.signals(2).values), 10);
            
            % 提取10位数字输出
            for bit_idx = 1:10
                ramp_binary_output(:, bit_idx) = ramp_correction_output.signals(bit_idx + 1).values;
            end
            
            % 调用静态特性计算函数
            [dnl, inl, static_metrics] = calculate_static_performance(...
                ramp_signal, ramp_binary_output, params_ramp, t_ramp);
            
            % 创建静态测试数据结构体
            static_test_data = create_static_test_data_struct(...
                dnl, inl, static_metrics, ramp_signal, ramp_binary_output, t_ramp);
            
            fprintf('✓ 静态特性测试完成: DNL_max=%.3f LSB, INL_max=%.3f LSB\n', ...
                    static_metrics.max_dnl, static_metrics.max_inl);
            static_test_success = true;
            
        else
            error('斜坡信号ADC输出结构体无效');
        end
        
    catch ME
        fprintf('✗ 静态特性测试失败: %s\n', ME.message);
        static_test_success = false;
    end
else
    fprintf('\n[步骤2/2] 跳过静态特性测试（根据配置）\n');
end

%% 可视化结果
% 动态特性可视化
if dynamic_test_success && exist('dynamic_test_data', 'var') && ~isempty(dynamic_test_data)
    fprintf('\n[可视化] 生成动态特性测试图形...\n');
    
    try
        generate_visualization_plots(dynamic_test_data, t_sine, sine_signal);
        fprintf('✓ 动态特性可视化完成\n');
    catch vis_error
        fprintf('! 动态特性可视化失败: %s\n', vis_error.message);
    end
else
    fprintf('\n[可视化] 跳过动态特性可视化（测试未启用或数据不可用）\n');
end

% 静态特性可视化
if static_test_success && exist('static_test_data', 'var') && ~isempty(static_test_data)
    fprintf('\n[可视化] 生成静态特性测试图形...\n');
    
    try
        generate_static_visualization_plots(static_test_data);
        fprintf('✓ 静态特性可视化完成\n');
    catch vis_error
        fprintf('! 静态特性可视化失败: %s\n', vis_error.message);
    end
else
    fprintf('\n[可视化] 跳过静态特性可视化（数据不可用）\n');
end

%% 步骤3: 综合结果评估
fprintf('\n[步骤3/3] 综合结果评估...\n');

% 更新测试结果评估逻辑
test_results = [];
test_names = {};

if test_config.run_dynamic_test
    test_results(end+1) = dynamic_test_success;
    test_names{end+1} = '动态性能测试（正弦波）';
end

if test_config.run_static_test
    test_results(end+1) = static_test_success;
    test_names{end+1} = '静态特性测试（斜坡信号）';
end

fprintf('\n%s\n', repmat('=', 1, 60));
fprintf('流水线ADC静态特性测试结果汇总\n');
fprintf('%s\n', repmat('=', 1, 60));

for i = 1:length(test_results)
    status = {'✗ 失败', '✓ 通过'};
    fprintf('%s: %s\n', test_names{i}, status{test_results(i) + 1});
end

passed_tests = sum(test_results);
total_tests = length(test_results);
fprintf('\n总体完成度: %d/%d (%.1f%%)\n', passed_tests, total_tests, 100*passed_tests/total_tests);

% 输出静态特性指标
if static_test_success && exist('static_test_data', 'var')
    fprintf('\n=== 静态特性测试 ===\n');
    fprintf('DNL: RMS=%.3f LSB, Max=%.3f LSB\n', static_test_data.static_metrics.rms_dnl, static_test_data.static_metrics.max_dnl);
    fprintf('INL: RMS=%.3f LSB, Max=%.3f LSB\n', static_test_data.static_metrics.rms_inl, static_test_data.static_metrics.max_inl);
    

end

% 输出动态特性指标（如果启用了动态测试）
if dynamic_test_success && exist('dynamic_test_data', 'var')
    fprintf('\n=== 动态性能测试 ===\n');
    fprintf('信号频率: %.3f MHz, 幅度: ±%.3f V\n', params.f_in/1e6, max(abs(sine_signal))/2);
    fprintf('SNR: %.1f dB, SNDR: %.1f dB, SFDR: %.1f dB, ENOB: %.1f bits\n', ...
            dynamic_test_data.snr, dynamic_test_data.sndr, dynamic_test_data.sfdr, dynamic_test_data.enob);
end

% 测试完成提示
fprintf('\n=== 测试完成 ===\n');


%% 辅助函数：创建动态测试数据结构体
function dynamic_test_data = create_dynamic_test_data_struct(...
    snr, sndr, sfdr, enob, performance_metrics, ...
    pipeline_output, clkh_signal, clks_signal, binary_output, raw_analog_data)
%CREATE_DYNAMIC_TEST_DATA_STRUCT 创建统一的动态测试数据结构体
%   整合所有测试相关数据，避免重复存储和冗余字段

    % 创建主数据结构体
    dynamic_test_data = struct();

    % 存储核心性能指标（避免重复存储）
    dynamic_test_data.snr = snr;
    dynamic_test_data.sndr = sndr;
    dynamic_test_data.sfdr = sfdr;
    dynamic_test_data.enob = enob;

    % 存储完整的性能指标结构体
    dynamic_test_data.performance_metrics = performance_metrics;

    % 存储测试数据
    dynamic_test_data.pipeline_output = pipeline_output;
    dynamic_test_data.clkh_signal = clkh_signal;
    dynamic_test_data.clks_signal = clks_signal;
    dynamic_test_data.binary_output = binary_output;
    dynamic_test_data.raw_analog_data = raw_analog_data;
end

%% 可视化函数：生成动态特性测试图形
function generate_visualization_plots(dynamic_test_data, t_sampled, signal_input)
    plot_data = dynamic_test_data.performance_metrics.plot_data;

    % 图1: 频谱分析（专注于频域分析结果）
    figure('Name', 'Pipeline ADC Spectrum Analysis', 'Position', [100, 100, 1400, 600]);

    % 频谱分析图（单图显示）
        plot(plot_data.freq_axis_MHz, plot_data.magnitude_db, 'b-', 'LineWidth', 1.2);

        % 标记基频和谐波
        hold on;
            plot(plot_data.fundamental_freq_MHz, plot_data.fundamental_magnitude_db, ...
                 'ro', 'MarkerSize', 10, 'LineWidth', 2);
            text(plot_data.fundamental_freq_MHz, plot_data.fundamental_magnitude_db+3, ...
                 'f_0', 'FontSize', 12, 'FontWeight', 'bold');

            % 注意：谐波标记功能已移除，仅保留主频标记
        hold off;

        xlim([0, plot_data.nyquist_freq_MHz]);
    xlabel('频率 (MHz)');
    ylabel('幅度 (dB)');
    title('流水线ADC频谱分析');
    grid on;
    
    % 图2: 时域分析
    figure('Name', 'Pipeline ADC Time Domain Analysis', 'Position', [150, 150, 1400, 800]);
    
    % 获取2us时间范围数据
    time_limit_indices = find(t_sampled <= 2e-6);
    
    % 子图1: 输入输出对比
    subplot(2, 1, 1);
    plot(t_sampled(time_limit_indices)*1e6, signal_input(time_limit_indices), ...
         'b-', 'LineWidth', 2, 'DisplayName', 'Input Signal');
    hold on;
    plot(t_sampled(time_limit_indices)*1e6, ...
         dynamic_test_data.pipeline_output(time_limit_indices), ...
         'r.-', 'MarkerSize', 2, 'LineWidth', 1, 'DisplayName', 'ADC Output');
    hold off;
    
    xlabel('时间 (μs)');
    ylabel('幅度 (V)');
    title('输入信号与ADC输出对比');
    legend('show', 'Location', 'best');
    grid on;
    
    % 子图2: 非重叠时钟波形
    subplot(2, 1, 2);

    % 显示CLKH（保持时钟）
    plot(t_sampled(time_limit_indices)*1e6, ...
         dynamic_test_data.clkh_signal(time_limit_indices), ...
         'r-', 'LineWidth', 1.5, 'DisplayName', 'CLKH (保持时钟)');

    hold on;

    % 显示CLKS（采样时钟）
    plot(t_sampled(time_limit_indices)*1e6, ...
         dynamic_test_data.clks_signal(time_limit_indices) + 1.2, ...
         'b-', 'LineWidth', 1.5, 'DisplayName', 'CLKS (采样时钟)');

    hold off;

    xlabel('时间 (μs)');
    ylabel('时钟电平');
    title('非重叠时钟波形 (CLKH & CLKS)');
    legend('show', 'Location', 'best');
    ylim([-0.2, 2.4]);  % 设置合适的Y轴范围以清晰显示两个时钟
    grid on;
end

function static_test_data = create_static_test_data_struct(dnl, inl, static_metrics, ramp_signal, binary_output, time_ramp)
%CREATE_STATIC_TEST_DATA_STRUCT 创建静态测试数据结构体
%   整合静态特性测试的所有相关数据
%
%   输入参数:
%       dnl - 差分非线性数组
%       inl - 积分非线性数组
%       static_metrics - 静态特性统计信息
%       ramp_signal - 斜坡输入信号
%       binary_output - 二进制输出数据
%       time_ramp - 时间向量
%
%   输出参数:
%       static_test_data - 静态测试数据结构体

    static_test_data = struct();
    
    % 存储DNL/INL数据
    static_test_data.dnl = dnl;
    static_test_data.inl = inl;
    static_test_data.static_metrics = static_metrics;
    
    % 存储测试数据
    static_test_data.ramp_signal = ramp_signal;
    static_test_data.binary_output = binary_output;
    static_test_data.time_ramp = time_ramp;
    
    % 添加数据标识
    static_test_data.test_type = 'static_linearity';
    static_test_data.measurement_method = 'code_density';
    
end

function generate_static_visualization_plots(static_test_data)
%GENERATE_STATIC_VISUALIZATION_PLOTS 生成静态特性测试图形
%   生成DNL/INL特性曲线和统计直方图
%
%   输入参数:
%       static_test_data - 静态测试数据结构体

    dnl = static_test_data.dnl;
    inl = static_test_data.inl;
    metrics = static_test_data.static_metrics;
    
    % 生成码值轴（对应ADC码值）
    code_axis = 0:(length(dnl)-1);
    
    % 图1: DNL/INL特性曲线
    figure('Name', 'Pipeline ADC Static Linearity Analysis', 'Position', [200, 200, 1400, 800]);
    
    % 子图1: DNL特性
    subplot(2, 2, 1);
    plot(code_axis, dnl, 'b-', 'LineWidth', 1);
    grid on;
    xlabel('数字码');
    ylabel('DNL (LSB)');
    title('差分非线性 (DNL)');
    ylim([min(dnl)-0.1, max(dnl)+0.1]);
    
    % 添加统计信息
    text(0.05, 0.95, sprintf('RMS: %.3f LSB\nMax: %.3f LSB', ...
         metrics.rms_dnl, metrics.max_dnl), ...
         'Units', 'normalized', 'VerticalAlignment', 'top', ...
         'BackgroundColor', 'white', 'EdgeColor', 'black');
    
    % 子图2: INL特性
    subplot(2, 2, 2);
    plot(code_axis, inl, 'r-', 'LineWidth', 1);
    grid on;
    xlabel('数字码');
    ylabel('INL (LSB)');
    title('积分非线性 (INL)');
    ylim([min(inl)-0.1, max(inl)+0.1]);
    
    % 添加统计信息
    text(0.05, 0.95, sprintf('RMS: %.3f LSB\nMax: %.3f LSB', ...
         metrics.rms_inl, metrics.max_inl), ...
         'Units', 'normalized', 'VerticalAlignment', 'top', ...
         'BackgroundColor', 'white', 'EdgeColor', 'black');
    
    % 子图3: DNL直方图
    subplot(2, 2, 3);
    histogram(dnl, 50, 'FaceColor', 'blue', 'FaceAlpha', 0.7);
    grid on;
    xlabel('DNL (LSB)');
    ylabel('频次');
    title('DNL分布直方图');
    
    % 子图4: INL直方图
    subplot(2, 2, 4);
    histogram(inl, 50, 'FaceColor', 'red', 'FaceAlpha', 0.7);
    grid on;
    xlabel('INL (LSB)');
    ylabel('频次');
    title('INL分布直方图');
    
    % 图2: 斜坡输入与码密度分析
    figure('Name', 'Ramp Input and Code Density Analysis', 'Position', [250, 250, 1400, 600]);
    
    % 子图1: 斜坡输入信号（显示前200μs）
    subplot(1, 2, 1);
    time_us = static_test_data.time_ramp * 1e6;
    display_indices = find(time_us <= 200);  % 显示前200μs
    
    if ~isempty(display_indices)
        plot(time_us(display_indices), static_test_data.ramp_signal(display_indices), ...
             'g-', 'LineWidth', 1.5);
        grid on;
        xlabel('时间 (μs)');
        ylabel('幅度 (V)');
        title('斜坡输入信号');
    end
    
    % 子图2: 码密度分布
    subplot(1, 2, 2);
    % 计算权重码用于码密度分析
    weights = 2.^(9:-1:0);
    weighted_codes = static_test_data.binary_output * weights' - 512;
    
    histogram(weighted_codes, 64, 'FaceColor', 'green', 'FaceAlpha', 0.7);
    grid on;
    xlabel('数字码值');
    ylabel('出现频次');
    title('码密度分布');
    
    % 添加统计信息
    text(0.05, 0.95, sprintf('总样本数: %d\n码值范围: [%d, %d]', ...
         length(weighted_codes), min(weighted_codes), max(weighted_codes)), ...
         'Units', 'normalized', 'VerticalAlignment', 'top', ...
         'BackgroundColor', 'white', 'EdgeColor', 'black');
    
end