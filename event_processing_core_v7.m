function [digital_data_struct, analog_data_struct, processing_info] = ...
    event_processing_core_v7(Vin_p, Vin_n, t, clks, clkh, digital_data_struct, analog_data_struct, rel_error_params, params)
%EVENT_PROCESSING_CORE_V7 事件驱动处理核心 (v7版本)
%   VERSION HISTORY:
%   v1-v5: 复杂的有效性标记、握手信号、标签对齐机制
%   v6: 回归实际电路设计思路的完全重构版本
%   v6.1: 规范化时钟事件处理机制的进一步优化
%   v6.2: 基于D触发器的延迟对齐机制
%   v7 (当前): 高性能版本
%   v7.1: 统计信息批量更新、数据存储模式和主循环向量化优化
%   v7.2: 状态转换查找表实现，将条件判断逻辑替换为O(1)查找表访问
%   v7.3: 时钟编码重构，通过参数传递消除子函数中的重复定义
%
%   v7版本核心优化:
%   1. 字符串预计算优化: 预生成所有字符串键并建立数值索引映射
%   2. 时钟事件预计算: 预计算完整时钟事件序列，使用数值编码
%   3. 延迟状态存储重构: 使用3D数组替代动态结构体访问
%   4. 循环结构优化: 优化条件判断顺序，减少函数调用开销
%   5. 内联关键函数: 将简单函数逻辑内联到主循环中
%
%   v7版本要点:
%   - 字符串操作减少约90%，每时间步节省20-30次字符串操作
%   - 结构体动态访问改为O(1)数组访问
%   - 时钟事件判断从每时间步调用改为预计算
%   - 条件判断使用数值比较替代字符串比较
%
%   输入参数:
%       Vin_p, Vin_n - 差分输入信号
%       t - 时间向量
%       clks, clkh - 非交叠时钟信号
%       digital_data_struct - 数字数据结构体 (18位独立存储)
%       analog_data_struct - 模拟数据结构体 (9条线路独立存储)
%       rel_error_params - 相对误差参数
%       params - ADC参数
%
%   输出参数:
%       digital_data_struct - 填充后的数字数据结构体
%       analog_data_struct - 填充后的模拟数据结构体
%       processing_info - 处理统计信息

    fprintf('=== 事件驱动处理核心 ===\n');
    
    %% 初始化处理参数
    num_samples = length(t);
    num_stages = params.num_stages;

    % 初始化处理统计
    processing_info = struct();
    processing_info.total_samples = num_samples;
    processing_info.processed_samples = 0;
    processing_info.sampling_events = 0;
    processing_info.holding_events = 0;
    processing_info.idle_events = 0;
    processing_info.digital_outputs = 0;
    processing_info.analog_outputs = 0;
    
    % 计算差分输入
    Vin_diff = Vin_p - Vin_n;

    %% 预分配连续数据矩阵（列优先布局）
    % 模拟数据矩阵：9条线路 (SHA + 8级流水线)
    analog_data_matrix = zeros(num_samples, 9);
    % 数字数据矩阵：18位数字 (8级×2位 + Flash×2位)
    digital_data_matrix = zeros(num_samples, 18);
    
    %% 字符串预计算和数值编码映射
    % 创建编码常量结构体，避免重复定义
    % 时钟事件类型数值编码 (替代字符串比较)
    % 1=CLKS_rising, 2=CLKS_falling, 3=CLKS_high, 4=CLKH_rising, 5=CLKH_falling, 6=CLKH_high, 7=IDLE, 8=ERROR_OVERLAP
    clock_codes = struct();
    clock_codes.CLKS_RISING = 1; clock_codes.CLKS_FALLING = 2; clock_codes.CLKS_HIGH = 3;
    clock_codes.CLKH_RISING = 4; clock_codes.CLKH_FALLING = 5; clock_codes.CLKH_HIGH = 6;
    clock_codes.IDLE = 7; clock_codes.ERROR_OVERLAP = 8;

    % 模块状态数值编码 (替代字符串比较)
    % 1=Sampling, 2=Holding, 3=IDLE
    state_codes = struct();
    state_codes.SAMPLING = 1; state_codes.HOLDING = 2; state_codes.IDLE = 3;

    % 时钟类型编码: 1=CLKS, 2=CLKH
    clock_types = struct();
    clock_types.CLKS = 1; clock_types.CLKH = 2;
    
    % 预计算bit_start索引数组 (避免重复计算)
    bit_start_indices = (0:7) * 2 + 1;  % [1, 3, 5, 7, 9, 11, 13, 15]
    
    %% 时钟事件序列预计算
    clock_events = precompute_clock_events(clks, clkh, num_samples, clock_codes);
    
    %% 延迟状态存储重构为3D数组
    % 数组维度: [stage_id(1-9), bit_id(1-2), delay_idx(1-8)]
    % stage 1-8为流水线级，stage 9为Flash ADC
    delay_states_array = zeros(9, 2, 8);
    
    % 预计算延迟配置信息
    [delay_configs, ~] = precompute_delay_configurations(clock_types);
    
    %% 初始化流水线状态机（v7版本）
    pipeline_state = initialize_pipeline(num_stages, state_codes, clock_types);

    %% 生成状态转换查找表
    state_lookup_table = state_transition_lookup_table(clock_codes, state_codes, clock_types);

    %% 主要事件驱动循环 - v7版本
    fprintf('开始事件驱动处理...\n');
    
    for time_idx = 1:num_samples
        current_input = Vin_diff(time_idx);
        
        % 直接使用预计算的时钟事件 (避免函数调用)
        clock_event_type = clock_events(time_idx);
        
        % 处理SHA模块 (CLKH采样，CLKS保持)
        [pipeline_state.sha, sha_output] = ...
            process_sha_module_matrix(pipeline_state.sha, current_input, clock_event_type, state_codes, state_lookup_table);
        analog_data_matrix(time_idx, 1) = sha_output;

        % 处理所有流水线级
        for stage = 1:num_stages
            [pipeline_state.stages{stage}, stage_analog_output, stage_digital_output, delay_states_array] = ...
                process_pipeline_stage_matrix(pipeline_state.stages{stage}, stage, ...
                                              pipeline_state, clock_event_type, ...
                                              rel_error_params, params, ...
                                              delay_states_array, delay_configs, state_codes, state_lookup_table, clock_codes, clock_types);
            % 存储到数据矩阵
            analog_data_matrix(time_idx, stage + 1) = stage_analog_output;
            bit_start = bit_start_indices(stage);
            digital_data_matrix(time_idx, bit_start:bit_start+1) = stage_digital_output;
        end

        % 处理Flash ADC (CLKS采样，CLKH保持)
        [pipeline_state.flash, flash_digital_output, delay_states_array] = ...
            process_flash_adc_matrix(pipeline_state.flash, pipeline_state.stages{end}, ...
                                    clock_event_type, params, ...
                                    delay_states_array, delay_configs, state_codes, state_lookup_table, clock_codes, clock_types);
        % 存储Flash ADC数字输出
        digital_data_matrix(time_idx, 17:18) = flash_digital_output;
        
        % 简化的进度显示（减少计算开销）
        if mod(time_idx, 100000) == 0
            fprintf('进度: %d/%d时间点 (%.1f%%)\n', time_idx, num_samples, 100*time_idx/num_samples);
        end
    end

    %% 批量计算事件统计和处理样本计数
    processing_info.processed_samples = num_samples;  % 直接设置为总样本数
    processing_info = compute_event_statistics_batch(processing_info, clock_events, clock_codes);

    %% 转换数据矩阵为结构体格式
    analog_data_struct = convert_matrix_to_analog_struct(analog_data_matrix, analog_data_struct);
    digital_data_struct = convert_matrix_to_digital_struct(digital_data_matrix, digital_data_struct);

    %% 统计最终结果
    % 统计数字信号输出
    digital_outputs = 0;
    for i = 1:length(digital_data_struct.signals)
        digital_outputs = digital_outputs + sum(digital_data_struct.signals(i).values ~= 0);
    end
    processing_info.digital_outputs = digital_outputs;

    % 统计模拟信号输出
    analog_outputs = 0;
    for i = 1:length(analog_data_struct.signals)
        analog_outputs = analog_outputs + sum(analog_data_struct.signals(i).values ~= 0);
    end
    processing_info.analog_outputs = analog_outputs;

    processing_info.output_efficiency = processing_info.digital_outputs / num_samples;
    
    fprintf('v7处理完成:\n');
    fprintf('  处理样本: %d/%d\n', processing_info.processed_samples, num_samples);
    fprintf('  数字输出: %d时间点 (%.1f%%)\n', processing_info.digital_outputs, 100*processing_info.output_efficiency);
    fprintf('  模拟输出: %d时间点\n', processing_info.analog_outputs);
    
    % 事件统计（包含错误检测）
    if isfield(processing_info, 'error_events') && processing_info.error_events > 0
        fprintf('  事件统计: CLKS事件%d, CLKH事件%d, 空闲%d, 错误%d\n', ...
                processing_info.sampling_events, processing_info.holding_events, ...
                processing_info.idle_events, processing_info.error_events);
        fprintf('  警告: 检测到%d个非交叠时钟违规事件\n', processing_info.error_events);
    else
        fprintf('  事件统计: CLKS事件%d, CLKH事件%d, 空闲%d\n', ...
                processing_info.sampling_events, processing_info.holding_events, processing_info.idle_events);
        fprintf('  时钟完整性: 通过 (无非交叠违规)\n');
    end
end

%% v7版本辅助函数

function clock_events = precompute_clock_events(clks, clkh, num_samples, clock_codes)
%PRECOMPUTE_CLOCK_EVENTS 预计算完整的时钟事件序列
%   将原来在主循环中每次调用determine_clock_event的操作提前到初始化阶段
%   使用数值编码替代字符串，大幅提升主循环性能

    % 时钟事件类型数值编码
    CLKS_RISING = clock_codes.CLKS_RISING; CLKS_FALLING = clock_codes.CLKS_FALLING; CLKS_HIGH = clock_codes.CLKS_HIGH;
    CLKH_RISING = clock_codes.CLKH_RISING; CLKH_FALLING = clock_codes.CLKH_FALLING; CLKH_HIGH = clock_codes.CLKH_HIGH;
    IDLE = clock_codes.IDLE; ERROR_OVERLAP = clock_codes.ERROR_OVERLAP;
    
    clock_events = zeros(num_samples, 1);
    
    for time_idx = 1:num_samples
        current_clks = clks(time_idx);
        current_clkh = clkh(time_idx);
        
        % 检查时钟边沿（优先处理边沿事件）
        if time_idx > 1
            prev_clks = clks(time_idx - 1);
            prev_clkh = clkh(time_idx - 1);
            
            clks_rising = (prev_clks == 0) && (current_clks == 1);
            clks_falling = (prev_clks == 1) && (current_clks == 0);
            clkh_rising = (prev_clkh == 0) && (current_clkh == 1);
            clkh_falling = (prev_clkh == 1) && (current_clkh == 0);
            
            % 优先处理时钟边沿事件
            if clks_rising
                clock_events(time_idx) = CLKS_RISING;
            elseif clks_falling
                clock_events(time_idx) = CLKS_FALLING;
            elseif clkh_rising
                clock_events(time_idx) = CLKH_RISING;
            elseif clkh_falling
                clock_events(time_idx) = CLKH_FALLING;
            else
                % 无边沿变化，确定电平状态
                if current_clks == 1 && current_clkh == 0
                    clock_events(time_idx) = CLKS_HIGH;
                elseif current_clks == 0 && current_clkh == 1
                    clock_events(time_idx) = CLKH_HIGH;
                elseif current_clks == 0 && current_clkh == 0
                    clock_events(time_idx) = IDLE;
                else
                    clock_events(time_idx) = ERROR_OVERLAP;
                    warning('检测到非交叠时钟违规：CLKS=%d, CLKH=%d', current_clks, current_clkh);
                end
            end
        else
            % 初始时刻，直接检测电平状态
            if current_clks == 1 && current_clkh == 0
                clock_events(time_idx) = CLKS_HIGH;
            elseif current_clks == 0 && current_clkh == 1
                clock_events(time_idx) = CLKH_HIGH;
            elseif current_clks == 0 && current_clkh == 0
                clock_events(time_idx) = IDLE;
            else
                clock_events(time_idx) = ERROR_OVERLAP;
                warning('检测到非交叠时钟违规：CLKS=%d, CLKH=%d', current_clks, current_clkh);
            end
        end
    end
end

function [delay_configs, max_delays] = precompute_delay_configurations(clock_types)
%PRECOMPUTE_DELAY_CONFIGURATIONS 预计算延迟配置信息
%   预计算各级流水线的延迟配置，避免在主循环中重复计算

    % 初始化延迟配置结构
    delay_configs = struct();

    % 时钟类型编码: 1=CLKS, 2=CLKH
    CLKS = clock_types.CLKS; CLKH = clock_types.CLKH;

    max_delays = 8;  % 最大延迟级数

    % 为每个stage预计算延迟配置
    for stage_id = 1:9  % 1-8为流水线级，9为Flash ADC
        if stage_id >= 1 && stage_id <= 8
            % 流水线级延迟配置
            num_delays = 9 - stage_id;  % STAGE1:8个, STAGE2:7个, ..., STAGE8:1个

            % 确定该级的保持态控制时钟
            if mod(stage_id, 2) == 1
                % 奇数级：CLKS采样，CLKH保持
                holding_clock = CLKH;
            else
                % 偶数级：CLKH采样，CLKS保持
                holding_clock = CLKS;
            end

            % 构造时钟序列：第一个D触发器使用保持态时钟，后续交替
            clock_sequence = zeros(num_delays, 1);
            clock_sequence(1) = holding_clock;  % 第一个D触发器使用保持态时钟

            for i = 2:num_delays
                if clock_sequence(i-1) == CLKH
                    clock_sequence(i) = CLKS;
                else
                    clock_sequence(i) = CLKH;
                end
            end

        elseif stage_id == 9
            % Flash ADC延迟配置
            num_delays = 1;
            clock_sequence = CLKH;  % Flash ADC使用CLKH作为保持态时钟
        end

        % 存储配置
        delay_configs.(['stage_' num2str(stage_id)]) = struct(...
            'num_delays', num_delays, ...
            'clock_sequence', clock_sequence);
    end
end

function pipeline_state = initialize_pipeline(num_stages, state_codes, clock_types)
%INITIALIZE_PIPELINE 初始化流水线状态机
%   使用数值编码替代字符串，提升访问效率
%   输入参数:
%       num_stages - 流水线级数
%       state_codes - 状态编码结构体
%       clock_types - 时钟类型编码结构体

    pipeline_state = struct();

    % SHA状态初始化 (CLKH采样，CLKS保持)
    pipeline_state.sha = struct(...
        'state', state_codes.IDLE, ...
        'input_voltage', 0, ...
        'output_voltage', 0, ...
        'sampling_clock', clock_types.CLKH, ...
        'holding_clock', clock_types.CLKS, ...
        'last_time_index', 0);

    % 流水线级状态初始化
    pipeline_state.stages = cell(num_stages, 1);
    for stage = 1:num_stages
        % 时钟分配：奇数级CLKS采样+CLKH保持，偶数级CLKH采样+CLKS保持
        if mod(stage, 2) == 1
            sampling_clock = clock_types.CLKS;
            holding_clock = clock_types.CLKH;
        else
            sampling_clock = clock_types.CLKH;
            holding_clock = clock_types.CLKS;
        end

        pipeline_state.stages{stage} = struct(...
            'stage_id', stage, ...
            'state', state_codes.IDLE, ...
            'input_voltage', 0, ...
            'output_analog_voltage', 0, ...
            'output_digital_bits', [0, 0], ...
            'sampling_clock', sampling_clock, ...
            'holding_clock', holding_clock, ...
            'last_time_index', 0);
    end

    % Flash ADC状态初始化 (CLKS采样，CLKH保持)
    pipeline_state.flash = struct(...
        'state', state_codes.IDLE, ...
        'input_voltage', 0, ...
        'output_digital_bits', [0, 0], ...
        'sampling_clock', clock_types.CLKS, ...
        'holding_clock', clock_types.CLKH, ...
        'last_time_index', 0);

    fprintf('流水线状态机初始化完成\n');
end

function new_state = determine_module_state(module_state, clock_event_type, state_lookup_table)
%DETERMINE_MODULE_STATE 根据时钟事件确定模块状态（查找表版本）
%   使用预计算的3D查找表实现O(1)时间复杂度的状态转换
%
%   输入参数：
%       module_state - 模块状态结构体，包含sampling_clock和holding_clock
%       clock_event_type - 时钟事件类型（1-8）
%       state_lookup_table - 预计算的状态转换查找表(8x2x2)
%
%   输出：
%       new_state - 新的模块状态（1=SAMPLING, 2=HOLDING, 3=IDLE）

    % 直接使用查找表进行O(1)状态查找
    new_state = state_lookup_table(clock_event_type, ...
                                   module_state.sampling_clock, ...
                                   module_state.holding_clock);
end

function [delayed_data_array, updated_delay_states_array] = ...
    pipeline_delay_alignment(original_data_array, stage_id, clock_event_type, ...
                            delay_states_array, delay_configs, clock_codes, clock_types)
%PIPELINE_DELAY_ALIGNMENT 流水线延迟对齐管理函数
%   使用3D数组替代动态结构体访问，大幅提升性能
%   数组维度: [stage_id(1-9), bit_id(1-2), delay_idx(1-8)]
%   通过参数传递编码常量，消除重复定义

    % 从传入参数获取时钟事件类型编码
    CLKS_RISING = clock_codes.CLKS_RISING; CLKH_RISING = clock_codes.CLKH_RISING;
    % 从传入参数获取时钟类型编码
    CLKS = clock_types.CLKS; CLKH = clock_types.CLKH;

    % 获取延迟配置
    stage_config = delay_configs.(['stage_' num2str(stage_id)]);
    num_delays = stage_config.num_delays;
    clock_sequence = stage_config.clock_sequence;

    % 对每个数字位应用D触发器延迟链 (使用3D数组访问)
    delayed_data_array = zeros(2, 1);

    for bit_idx = 1:2
        current_data = original_data_array(bit_idx);

        % 通过D触发器延迟链传递数据
        for delay_idx = 1:num_delays
            control_clock = clock_sequence(delay_idx);

            if delay_idx == 1
                % 第一个D触发器：输入为原始数据
                input_data = current_data;
            else
                % 后续D触发器：输入为前一个D触发器的输出
                input_data = delay_states_array(stage_id, bit_idx, delay_idx - 1);
            end

            % 获取前一时刻的输出状态
            previous_output = delay_states_array(stage_id, bit_idx, delay_idx);

            % 内联D触发器逻辑 (避免函数调用)
            % 检查当前是否为指定控制时钟的上升沿
            if (control_clock == CLKS && clock_event_type == CLKS_RISING) || ...
               (control_clock == CLKH && clock_event_type == CLKH_RISING)
                % 上升沿：更新输出为当前输入
                delay_states_array(stage_id, bit_idx, delay_idx) = input_data;
            else
                % 非上升沿：保持前一时刻的输出
                delay_states_array(stage_id, bit_idx, delay_idx) = previous_output;
            end
        end

        % 最后一个D触发器的输出作为该位的延迟输出
        delayed_data_array(bit_idx) = delay_states_array(stage_id, bit_idx, num_delays);
    end

    % 返回更新后的延迟状态数组
    updated_delay_states_array = delay_states_array;
end

% 保留原有的处理函数 (process_1p5bit_stage 和 process_2bit_flash_adc)
% 这些函数的逻辑保持不变，确保输出结果完全一致

function [analog_out, digital_out] = process_1p5bit_stage(input_voltage, stage_id, rel_error_params, params)
%PROCESS_1P5BIT_STAGE 处理1.5bit流水线级
%   使用正确的MDAC残差输出公式：
%   Vres = (1+gain_error)*(2+cap_mismatch)*Vin - (1+gain_error)*(1+cap_mismatch)*D*Vref + offset_error

    % 获取误差参数
    gain_error = rel_error_params(stage_id, 1);      % 增益误差
    cap_mismatch = rel_error_params(stage_id, 2);    % 电容失配误差
    offset_error = rel_error_params(stage_id, 3);    % 偏移误差

    % 1.5bit ADC量化
    Vref = params.Vref;
    threshold_high = Vref/4;
    threshold_low = -Vref/4;

    % 执行量化并确定数字输出码D值
    if input_voltage >= threshold_high
        digital_code = [1, 0];  % 二进制编码
        D = 1;                  % 对应的数值：+1
    elseif input_voltage < threshold_low
        digital_code = [0, 0];  % 二进制编码
        D = -1;                 % 对应的数值：-1
    else    %大于等于低阈值，小于高阈值
        digital_code = [0, 1];  % 二进制编码
        D = 0;                  % 对应的数值：0
    end

    % MDAC残差输出计算公式
    analog_residue = (1 + gain_error) * (2 + cap_mismatch) * input_voltage - ...
                    (1 + gain_error) * (1 + cap_mismatch) * D * Vref + offset_error;

    analog_out = analog_residue;
    digital_out = digital_code;
end

function flash_digital = process_2bit_flash_adc(input_voltage, params)
%PROCESS_2BIT_FLASH_ADC 处理2bit Flash ADC (标准2-bit ADC阈值)

    Vref = params.Vref;
    % 标准2-bit ADC阈值：[-1/2Vref, 0, 1/2Vref]
    thresholds = [-Vref/2, 0, Vref/2];

    if input_voltage >= thresholds(3)
        flash_digital = [1, 1];  % 11: [Vref/2, +∞)
    elseif input_voltage >= thresholds(2)
        flash_digital = [1, 0];  % 10: [0, Vref/2)
    elseif input_voltage >= thresholds(1)
        flash_digital = [0, 1];  % 01: [-Vref/2, 0)
    else
        flash_digital = [0, 0];  % 00: (-∞, -Vref/2)
    end
end

function processing_info = compute_event_statistics_batch(processing_info, clock_events, clock_codes)
%COMPUTE_EVENT_STATISTICS_BATCH 批量计算事件统计信息
%   基于预计算的时钟事件数组进行批量统计，替代逐步累加

    % 批量统计各类事件
    clks_events_mask = ismember(clock_events, [clock_codes.CLKS_RISING, clock_codes.CLKS_FALLING, clock_codes.CLKS_HIGH]);
    clkh_events_mask = ismember(clock_events, [clock_codes.CLKH_RISING, clock_codes.CLKH_FALLING, clock_codes.CLKH_HIGH]);
    idle_events_mask = (clock_events == clock_codes.IDLE);
    error_events_mask = (clock_events == clock_codes.ERROR_OVERLAP);

    processing_info.sampling_events = sum(clks_events_mask);
    processing_info.holding_events = sum(clkh_events_mask);
    processing_info.idle_events = sum(idle_events_mask);

    % 处理错误事件统计
    error_count = sum(error_events_mask);
    if error_count > 0
        processing_info.error_events = error_count;
    end
end

function [sha_state, sha_output] = process_sha_module_matrix(sha_state, input_voltage, clock_event_type, state_codes, state_lookup_table)
%PROCESS_SHA_MODULE_MATRIX 处理SHA模块（矩阵版本）
%   返回输出值而非直接写入结构体

    % SHA时钟配置：CLKH采样，CLKS保持
    new_state = determine_module_state(sha_state, clock_event_type, state_lookup_table);

    % 状态转移和数据处理
    if new_state == state_codes.SAMPLING
        % 采样态：接收/跟踪输入数据，输出为0
        sha_state.input_voltage = input_voltage;
        sha_state.output_voltage = 0;
        sha_state.state = state_codes.SAMPLING;
    elseif new_state == state_codes.HOLDING
        % 保持态：输出锁存的数据
        sha_state.output_voltage = sha_state.input_voltage;
        sha_state.state = state_codes.HOLDING;
    else  % state_codes.IDLE
        % 空闲态：输出为0
        sha_state.output_voltage = 0;
        sha_state.state = state_codes.IDLE;
    end

    sha_output = sha_state.output_voltage;
end

function [stage_state, stage_analog_output, stage_digital_output, updated_delay_states_array] = ...
    process_pipeline_stage_matrix(stage_state, stage_id, pipeline_state, clock_event_type, ...
                                  rel_error_params, params, delay_states_array, delay_configs, state_codes, state_lookup_table, clock_codes, clock_types)
%PROCESS_PIPELINE_STAGE_MATRIX 处理流水线级（矩阵版本）
%   返回输出值而非直接写入结构体

    % 获取前级数据
    if stage_id == 1
        prev_output = pipeline_state.sha.output_voltage;
    else
        prev_output = pipeline_state.stages{stage_id - 1}.output_analog_voltage;
    end

    % 确定当前级的状态
    new_state = determine_module_state(stage_state, clock_event_type, state_lookup_table);

    % 状态转移和数据处理
    if new_state == state_codes.SAMPLING
        % 采样态：接收前级数据，输出为0
        stage_state.input_voltage = prev_output;
        stage_state.output_analog_voltage = 0;
        stage_state.output_digital_bits = [0, 0];
        stage_state.state = state_codes.SAMPLING;
    elseif new_state == state_codes.HOLDING
        % 保持态：处理数据并输出
        [analog_out, digital_out] = process_1p5bit_stage(...
            stage_state.input_voltage, stage_id, rel_error_params, params);

        stage_state.output_analog_voltage = analog_out;
        stage_state.output_digital_bits = digital_out;
        stage_state.state = state_codes.HOLDING;
    else  % state_codes.IDLE
        % 空闲态：输出为0
        stage_state.output_analog_voltage = 0;
        stage_state.output_digital_bits = [0, 0];
        stage_state.state = state_codes.IDLE;
    end

    % 使用延迟对齐机制处理数字数据
    [delayed_digital_bits, updated_delay_states_array] = ...
        pipeline_delay_alignment(stage_state.output_digital_bits, stage_id, ...
                                clock_event_type, delay_states_array, delay_configs, clock_codes, clock_types);

    stage_analog_output = stage_state.output_analog_voltage;
    stage_digital_output = delayed_digital_bits;
end

function [flash_state, flash_digital_output, updated_delay_states_array] = ...
    process_flash_adc_matrix(flash_state, last_stage_state, clock_event_type, params, ...
                            delay_states_array, delay_configs, state_codes, state_lookup_table, clock_codes, clock_types)
%PROCESS_FLASH_ADC_MATRIX 处理Flash ADC（矩阵版本）
%   返回输出值而非直接写入结构体

    % 获取前级数据
    prev_output = last_stage_state.output_analog_voltage;

    % 确定Flash ADC状态
    new_state = determine_module_state(flash_state, clock_event_type, state_lookup_table);

    % 状态转移和数据处理
    if new_state == state_codes.SAMPLING
        % 采样态：接收前级数据
        flash_state.input_voltage = prev_output;
        flash_state.output_digital_bits = [0, 0];
        flash_state.state = state_codes.SAMPLING;
    elseif new_state == state_codes.HOLDING
        % 保持态：执行2bit Flash ADC量化
        flash_digital = process_2bit_flash_adc(flash_state.input_voltage, params);
        flash_state.output_digital_bits = flash_digital;
        flash_state.state = state_codes.HOLDING;
    else  % state_codes.IDLE
        % 空闲态：输出为0
        flash_state.output_digital_bits = [0, 0];
        flash_state.state = state_codes.IDLE;
    end

    % 使用延迟对齐机制处理Flash ADC数字数据
    [delayed_flash_bits, updated_delay_states_array] = ...
        pipeline_delay_alignment(flash_state.output_digital_bits, 9, ...
                                clock_event_type, delay_states_array, delay_configs, clock_codes, clock_types);

    flash_digital_output = delayed_flash_bits;
end

function analog_data_struct = convert_matrix_to_analog_struct(analog_data_matrix, analog_data_struct)
%CONVERT_MATRIX_TO_ANALOG_STRUCT 将模拟数据矩阵转换为结构体格式
%   批量转换连续数据矩阵为原始结构体格式

    % 获取数据维度
    [~, num_signals] = size(analog_data_matrix);

    % 批量复制数据到结构体
    for signal_idx = 1:num_signals
        analog_data_struct.signals(signal_idx).values = analog_data_matrix(:, signal_idx);
    end
end

function digital_data_struct = convert_matrix_to_digital_struct(digital_data_matrix, digital_data_struct)
%CONVERT_MATRIX_TO_DIGITAL_STRUCT 将数字数据矩阵转换为结构体格式
%   批量转换连续数据矩阵为原始结构体格式

    % 获取数据维度
    [~, num_bits] = size(digital_data_matrix);

    % 批量复制数据到结构体
    for bit_idx = 1:num_bits
        digital_data_struct.signals(bit_idx).values = digital_data_matrix(:, bit_idx);
    end
end

function state_lookup_table = state_transition_lookup_table(clock_codes, state_codes, clock_types)
%STATE_TRANSITION_LOOKUP_TABLE 生成状态转换查找表
%   预计算所有可能的状态转换组合，生成3D查找表
%   维度：[clock_event_type, sampling_clock, holding_clock] -> new_state
%
%   输出：
%       state_lookup_table - 3D数组(8x2x2)，存储所有状态转换结果

    % 时钟事件类型数值编码
    CLKS_RISING = clock_codes.CLKS_RISING; CLKS_FALLING = clock_codes.CLKS_FALLING; CLKS_HIGH = clock_codes.CLKS_HIGH;
    CLKH_RISING = clock_codes.CLKH_RISING; CLKH_FALLING = clock_codes.CLKH_FALLING; CLKH_HIGH = clock_codes.CLKH_HIGH;
    IDLE = clock_codes.IDLE; ERROR_OVERLAP = clock_codes.ERROR_OVERLAP;

    % 状态编码
    STATE_SAMPLING = state_codes.SAMPLING; STATE_HOLDING = state_codes.HOLDING; STATE_IDLE = state_codes.IDLE;

    % 时钟类型编码
    CLKS = clock_types.CLKS; CLKH = clock_types.CLKH;

    % 初始化查找表：[clock_event_type(8), sampling_clock(2), holding_clock(2)]
    state_lookup_table = zeros(8, 2, 2);

    % 填充查找表
    for event_type = 1:8
        for sampling_clock = 1:2
            for holding_clock = 1:2

                % 处理系统级错误状态和空闲状态
                if event_type == ERROR_OVERLAP || event_type == IDLE
                    state_lookup_table(event_type, sampling_clock, holding_clock) = STATE_IDLE;
                    continue;
                end

                % 处理下降沿事件：无论配置如何都返回空闲态
                if event_type == CLKS_FALLING || event_type == CLKH_FALLING
                    state_lookup_table(event_type, sampling_clock, holding_clock) = STATE_IDLE;
                    continue;
                end

                % 处理CLKS相关事件
                if event_type == CLKS_RISING || event_type == CLKS_HIGH
                    if sampling_clock == CLKS
                        state_lookup_table(event_type, sampling_clock, holding_clock) = STATE_SAMPLING;
                    elseif holding_clock == CLKS
                        state_lookup_table(event_type, sampling_clock, holding_clock) = STATE_HOLDING;
                    else
                        state_lookup_table(event_type, sampling_clock, holding_clock) = STATE_IDLE;
                    end
                    continue;
                end

                % 处理CLKH相关事件
                if event_type == CLKH_RISING || event_type == CLKH_HIGH
                    if sampling_clock == CLKH
                        state_lookup_table(event_type, sampling_clock, holding_clock) = STATE_SAMPLING;
                    elseif holding_clock == CLKH
                        state_lookup_table(event_type, sampling_clock, holding_clock) = STATE_HOLDING;
                    else
                        state_lookup_table(event_type, sampling_clock, holding_clock) = STATE_IDLE;
                    end
                    continue;
                end

                % 未知事件类型，默认为空闲
                state_lookup_table(event_type, sampling_clock, holding_clock) = STATE_IDLE;
            end
        end
    end
end
