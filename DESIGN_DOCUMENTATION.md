# 流水线ADC行为级模型与验证系统设计文档

## 1. 项目概述

本项目旨在开发一个精确、高效的流水线模数转换器（Pipelined ADC）行为级模型。该模型基于MATLAB事件驱动架构，回归实际电路设计思路，精确模拟了流水线ADC中各核心模块在非交叠时钟控制下的工作流程。

当前项目版本已完成理想ADC模型的设计与验证，在零误差参数下，模型能够正确输出与理论值完全一致的波形，证明了其架构的正确性和时序控制的精确性。

### 1.1. 项目目标

- **精确建模**: 建立一个与实际电路行为高度一致的流水线ADC模型，精确模拟采样保持、级间处理、延迟对齐和数字校正等环节。
- **性能验证**: 在理想条件下验证模型的输出正确性，为后续的误差分析和性能优化提供一个可靠的基准。
- **误差分析**: （未来目标）集成晶体管级电路的非理想因素，分析增益误差、电容失配、失调等对ADC动态性能（SNR, SNDR, ENOB等）的影响。
- **设计指导**: （未来目标）通过模型仿真反向计算出各级电路的误差容限，为低功耗、高精度ADC的晶体管级设计提供参数指导。

### 1.2. 系统特点

- **事件驱动架构**: 基于 `v6`事件处理核心，以非交叠时钟的边沿和电平状态作为驱动事件，精确控制每个模块的状态转换。
- **简化时序控制**: 移除了早期版本复杂的握手信号和标签对齐机制，采用更接近硬件实现的前置延迟对齐策略。
- **模块化设计**: 系统由信号生成、事件处理、数字校正等独立模块组成，结构清晰，易于维护和扩展。
- **结构化数据流**: 采用统一的 `struct`数据结构在模块间传递信号，包含了时间、模拟电压和数字位信息，便于追溯和分析。

## 2. 项目结构与核心模块

系统主要由一个主测试脚本和四个核心模块构成，共同完成流水线ADC的完整仿真流程。

### 2.1. 主测试与验证模块 (`main_pipeline_test.m`)

这是项目的顶层验证脚本，负责配置测试参数、调用仿真流程并对结果进行可视化和评估。其主要测试流程如下：

1. **参数配置**: 设置ADC的基本参数（如参考电压、级数、采样率、输入频率等）和误差参数矩阵（理想验证时设为全零）。
2. **信号生成**: 调用 `generate_timebase` 生成理想的输入正弦波和非交叠时钟信号。
3. **ADC仿真**: 调用 `event_driven_pipeline_adc_v4` 执行完整的流水线ADC仿真，获取最终的校正输出和各级原始数据。
4. **理想对比**: 运行一个独立的理想ADC模型 (`run_ideal_adc_v2`)，生成用于对比的基准数据。
5. **结果可视化**: 绘制丰富的对比图，包括：
   - 流水线ADC与理想ADC的输出对比。
   - 各流水线级（SHA + 8级）的原始模拟输出波形。
   - 输入信号、最终输出信号与时钟信号的同步对比图。
6. **综合评估**: 汇总各步骤的成功状态，并输出详细的性能统计信息。

### 2.2. 核心设计模块

#### 2.2.1. 流水线ADC主体 (`event_driven_pipeline_adc_v4.m`)

该模块是整个事件驱动流水线ADC模型的顶层封装，负责协调和驱动整个仿真流程。它的核心职责是按顺序调用其他核心模块，管理数据流的传递。

- **功能**:
  1. 验证输入参数的正确性，特别是误差参数矩阵的格式。
  2. 调用 `generate_timebase.m` 创建仿真所需的时间基准、时钟信号和初始化数据结构。
  3. 调用 `event_processing_core_v6.m` 执行核心的事件驱动处理，模拟ADC的"模数转换"过程。
  4. 调用 `digital_correction_v4.m` 对事件处理核心输出的原始数字位进行校正，得到最终的10-bit数字输出和对应的模拟值。
- **数据流**:
  - **输入**: ADC参数 (`params`)、差分输入信号 (`Vin_p`, `Vin_n`)、相对误差参数 (`rel_error_params`)。
  - **输出**: 最终校正输出 (`correction_output_struct`)、事件处理统计 (`processing_info`)、数字校正统计 (`correction_metrics`) 以及各级原始模拟数据 (`raw_analog_data`)。

#### 2.2.2. 信号与时钟生成模块 (`generate_timebase.m`)

此模块负责创建整个仿真所需的基础信号和数据存储结构，是所有处理流程的起点。

- **功能**:
  1. **时间基准**: 生成一个具有10ps固定时间步长的高精度时间向量 `t`。
  2. **输入信号**: 生成一个与时间基准对应的标准正弦波 `sine_wave`。
  3. **非交叠时钟**: 生成两路占空比为48%、存在死区的非交叠时钟信号 `clks`和 `clkh`。
  4. **数据结构初始化**: 创建两个核心的数据结构体 `analog_data_struct`和 `digital_data_struct`，用于在整个仿真流程中存储和传递数据。这些结构体内部包含了与时间向量等长的、预分配好空间的信号数组。
- **输出**: 时间向量 `t`、正弦波 `sine_wave`、时钟信号 `clks`和 `clkh`，以及两个初始化为空白状态的数据结构体。

#### 2.2.3. 事件处理核心 (`event_processing_core_v6.m`)

回归电路的真实工作模式，精确模拟了在非交叠时钟驱动下，信号在各级流水线中的处理过程。

- **实现方式**:
  1. **事件判断**: 在 `for`循环的每个时间点，通过 `determine_clock_event`函数判断当前时刻的全局时钟事件（如 `CLKS_rising`, `CLKS_high`, `CLKH_falling`, `IDLE`等）。
  2. **状态机驱动**: 每个模块（SHA, Stage1-8, Flash ADC）根据全局时钟事件和自身的时钟配置（采样钟/保持钟）决定各自的状态（`Sampling`, `Holding`, `IDLE`）。例如，当 `CLKS_rising`事件发生时，所有以 `CLKS`为保持钟的模块进入 `Holding`状态，而所有以 `CLKS`为采样钟的模块进入 `Sampling`状态。
  3. **数据处理**:
     - 在 `Sampling`态，模块跟踪其前一级模块的输出。
     - 在 `Holding`态，模块锁存采样到的电压，并进行计算（如MDAC的残差放大或Flash ADC的量化），然后输出结果。
  4. **前置延迟对齐**: 在数据存入结构体时，数字位直接根据预设的延迟步数 (`delay_steps_array`) 计算出正确的存储索引，从而实现前置延迟对齐。这意味着当所有数据处理完毕后，`digital_data_struct`中的数据已经是延迟对齐的。
- **数据流**:
  - **输入**: 外部输入信号、时钟信号、空白的数据结构体、误差参数。
  - **输出**: 填充了各级原始模拟输出 (`analog_data_struct`) 和经过前置延迟对齐的各级原始数字输出 (`digital_data_struct`) 的数据结构体。

#### 2.2.4. 数字校正模块 (`digital_correction_v4.m`)

该模块接收已经过延迟对齐的18位原始数字码，并将其转换为标准的10位二进制输出。

- **实现方式**:
  1. **RSD校正**: 核心算法是 `execute_standard_full_adder_chain`，它通过一个标准全加器链将18位的冗余符号位（Redundant Signed Digit, RSD）数据累加校正为10位二进制码。该算法直接模拟了数字校正逻辑的硬件实现。
  2. **理想DAC转换**: 校正后的10位二进制码通过 `execute_ideal_dac_conversion`转换为模拟电压值。此过程包括权重赋值、中心点偏移和归一化，用于验证数字校正的正确性。
- **数据流**:
  - **输入**: 填充了预对齐18位原始数字码的 `digital_data_struct`。
  - **输出**: 一个新的 `correction_output_struct`结构体，其中包含最终的10位数字输出（每位一个独立信号）和经过理想DAC转换后的模拟输出。

## 3. 完整数据处理流程

流水线ADC模型的完整数据处理流程清晰地展示了各模块如何协同工作：

1. **初始化 (main_pipeline_test.m)**: 设置仿真参数。
2. **信号生成 (generate_timebase.m)**: 创建时间轴 `t`、输入信号 `sine_wave`、时钟 `clks/clkh`，以及空的 `analog_data_struct` 和 `digital_data_struct`。
3. **数据输入 (event_driven_pipeline_adc_v4.m)**: 将生成的信号和空的结构体传递给事件处理核心。
4. **事件驱动处理 (event_processing_core_v6.m)**:
   - 逐个时间点遍历。
   - 在每个时间点，SHA、8个流水线级和Flash ADC根据时钟事件更新自身状态（采样/保持/空闲）。
   - 处于 `Holding`态的模块进行计算，并将原始输出（模拟电压或数字位）存入 `analog_data_struct`和 `digital_data_struct`中。存储时，数据会根据预设的延迟被写入未来的时间索引，从而完成前置延迟对齐。
5. **数字校正 (digital_correction_v4.m)**:
   - 接收已对齐的18位原始数字数据。
   - 通过全加器链算法将18位RSD码转换为10位标准二进制码。
   - 将10位二进制码通过理想DAC转换为模拟值。
   - 将最终的10位数字输出和模拟输出打包成 `correction_output_struct`。
6. **结果输出 (event_driven_pipeline_adc_v4.m)**: 返回最终的校正结果、原始模拟数据和性能统计信息。
7. **分析与可视化 (main_pipeline_test.m)**: 将模型的输出与理想ADC的输出进行对比，并绘制图形以验证其正确性。

## 4. 流水线ADC工作原理与行为级模型详解

### 4.1. 总体架构

本设计使用经典的10-bit采样位宽、每级1.5-bit冗余采样的流水线ADC结构。ADC由一个采样保持放大器（SHA）、8级1.5-bit流水线子级（STAGE）、以及末级2-bit Flash ADC组成。这10级电路产生的18位原始数字输出被送至数字校正逻辑，最终输出标准的10-bit二进制码。整个系统由一对非交叠时钟（CLKS和CLKH）驱动。

### 4.2. 核心模块功能

- **SHA (采样保持放大器)**: 在 `CLKH`为高电平时对外部输入信号进行跟踪采样，在 `CLKS`为高电平时保持锁存的电压值并输出给第一级流水线。
- **STAGE (1.5-bit流水线级)**: 包含一个子ADC、一个子DAC和一个残差放大器。
  - **子ADC**: 将输入电压与阈值 `±Vref/4`比较，输出2位数字码（实现1.5-bit冗余）。
  - **子DAC**: 根据数字码输出 `-Vref`, `0`, 或 `+Vref`。
  - **残差放大器**: 将输入信号减去子DAC的输出，再将结果乘以2，得到残差电压 `Vres`输出给下一级。其理想传递函数为 $V_{out}(i)=2V_{in}(i)-D_i \cdot V_{ref}$。模型中实际使用的、包含非理想因素的传递函数为：

```matlab
Vres = (1+gain_error)*(2+cap_mismatch)*Vin - (1+gain_error)*(1+cap_mismatch)*D*Vref + offset_error
```

- **Flash ADC**: 末级2-bit闪速ADC，将最后一级的残差电压直接量化为2位二进制码。
- **Digital Correction (数字校正)**:
  - **延迟对齐**: 由于数据在流水线中逐级传递，每一级产生的数字码在时间上是错开的。本模型在 `event_processing_core_v6`中通过前置延迟写入的方式，确保了最终输出的18位原始数据在时间上是对齐的。
  - **RSD校正**: 通过一个全加器链，将对齐后的18位冗余数据精确地转换为10位标准二进制码。

### 4.3. 时序与状态机

- **非交叠时钟**: `CLKS`和 `CLKH`交替为高电平，确保相邻的奇数级和偶数级不会同时进行采样或保持操作，避免数据竞争。
- **交替工作模式**:
  - SHA、偶数级（2,4,6,8）: `CLKH`采样，`CLKS`保持。
  - 奇数级（1,3,5,7）、Flash ADC: `CLKS`采样，`CLKH`保持。
- **状态机**: 每个模块在任何时刻都处于 `Sampling`、`Holding`或 `IDLE`三种状态之一。状态的转换完全由时钟事件（上升沿、下降沿、高电平）决定，这精确地模拟了实际电路的行为。当一个模块（如STAGE1）在 `CLKH`的驱动下处于 `Holding`态并输出数据时，其下一级模块（STAGE2）正好在 `CLKH`的驱动下处于 `Sampling`态接收数据，从而实现了流水线式的数据处理。

## 5. 当前设计目标

### 5.1. 已完成成果

**动态特性计算函数**：

- 已完成专用的动态特性分析功能开发，包括信噪比（SNR）、信号与噪声失真比（SNDR）、有效位数（ENOB）、总谐波失真（THD）等关键性能指标的计算。
- 实现了频域分析算法，支持FFT变换、窗函数处理和谐波分量提取，确保动态特性计算的准确性和可重复性。
- 建立了标准化的性能评估接口，为误差模型验证和参数优化提供定量的评价基准。

### 5.2. 当前开发目标

基于已完成的动态特性计算功能，项目当前阶段将专注于性能优化和实际电路特性建模的演进。以下工作重点按优先级排序，旨在建立高效、完整的ADC性能分析和误差建模体系。

1. **模型性能优化**:

   - **计算加速优化**: 实现关键算法的计算加速，包括字符串预计算转数值索引技术，减少字符串比较操作的计算开销。
   - **存储结构优化**: 开发延迟状态的数组化存储方案，减少内存访问开销，提高数据处理效率。
   - **循环结构优化**: 优化基础循环结构，合并冗余操作，减少重复计算，提高整体处理效率。
   - **内存管理优化**: 改进数据结构的内存分配策略，减少动态内存操作，提升仿真运行速度。
2. **集成定量误差模型**:

   - 在现有理想流水线ADC模型基础上，集成可控的相对误差参数，包括各级MDAC的增益误差、电容失配误差和失调误差。
   - 开发参数化的误差注入机制，允许独立控制每一级的误差幅度和类型，实现对不同误差源影响的精确分析。
   - 建立三种模型的对比验证框架：理想模型（零误差基准）、定量误差模型（可控误差注入）、晶体管仿真结果（实际电路特性），通过输出结果的系统性比较验证误差建模的有效性。
3. **误差参数逆向计算**:

   - 基于晶体管仿真数据中观察到的实际误差特征，开发逆向参数提取算法，自动计算各级MDAC的等效误差参数。
   - 实现误差源分离技术，通过分析不同频率和幅度输入条件下的输出偏差，区分并量化增益误差、电容失配和失调误差的独立贡献。
   - 建立参数验证闭环，将逆向计算得到的误差参数代入定量误差模型，验证模型输出与晶体管仿真结果的一致性，确保误差建模的准确性和实用性。

### 5.3. 长远发展目标

**晶体管级电路设计程序**：

- **自动化设计脚本**: 开发基于HSPICE的自动化电路设计与优化脚本，实现从行为级规格到晶体管级电路的自动转换。
- **参数映射机制**: 建立行为模型与晶体管模型的双向参数映射机制，确保两个层次模型之间的一致性和可追溯性。
- **电路优化算法**: 实现基于误差敏感度分析的电路尺寸优化算法，通过自动调整晶体管尺寸和偏置条件，优化ADC的动态性能指标。
- **设计流程集成**: 建立完整的设计流程，从系统级规格分解到电路级实现，再到版图级优化的全流程自动化设计环境。

$N_{record}=pi*2^{N-1}*(Z_{α/2})^2/β^2$
