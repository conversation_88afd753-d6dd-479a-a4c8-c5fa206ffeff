clc
clear
 
%extract array data from the datasource file  
fileID = fopen('E:\data\matlab\100mV_0912_1.c','r');
[data4,~] =  fscanf(fileID,'%d');
data4 = data4';             %转置数组
 
numpt = 20000;              %数据点个数（可根据需求调节）
num_bit=12; %               %ADC数据位。（有符号整型数）
fclose(fileID);             %关闭文件
 
x = 1:1001;                 %作图点数
data100 = data4(1:1001);    %原始波形取作图点数个点
subplot(1,2,1);             %作图 一行 二列 中的第一个小图
plot(x,data100);            %作图，仅作波形示意，和后面的数据处理关系不大
 
mul=mean(data4);            %求数据平均值，用于后面的数据 正负中心对齐
 
 
%span=40;                    %信号频率+ -span范围都算作信号能量
span=max(round(numpt/200),5);%一种经验公式，来自maxim网站源码，基本正确，确实可以涵盖信号带宽                 
spanh=5;                     %谐波频率+ -spanh范围都算作谐波能量，可由小达到实验，ENOB数值稳定后即可
Fsample=125;                 %采样率125M，单位MHz
 
m=1:numpt;                                  %生成数组
 
data33=(data4(m)-mul)/(2^(num_bit-1)-1);    %正负中心（基线）对齐，并转化为具体幅度0-1；
 
                  
                                            % data33,需要做FFT变换的数据，4000个点，必须是连续的周期信号
beta=5.658;                                 %没有使用此变量
data44=data33'.*hanning(numpt);            %加窗，汉宁窗
%data44=data33'.*hamming(numpt);            %加窗，汉明窗
%data44=data33;                              
data55=fft(data44);                         %fft变换
data66=abs((data55));                       %取绝对值，表示信号幅度
data77=20*log10(data66);                    %取对数，分贝
data88=data66.*data66;                      %平方，表示信号能量
 
max_voltage =  max(abs(data33));            %最大电压值
max_voltagedb = 20*log10(max_voltage/1);    %最大电压分贝
%max_voltagedb = 0;                         %试验不设置为零的时候频谱图形状
 
bili_factor_1=1/max_voltage;                %幅度比例因子
bili_factor=bili_factor_1^2;                %功率比例因子
%bili_factor=1;                             %测试比例因子为1时参数变化    
 
maxdb=max(data77(1:numpt/2));               %最大幅值，分贝
 
x=((0:(numpt/2-1)).*Fsample/numpt);         %x轴，频率单位,Mhz，
y1=data77(1:numpt/2)-maxdb+max_voltagedb;   %y轴，幅值，db，最大0db
figure(1);
subplot(1,2,2)
plot(x,y1);                                 % 作图
 
fin=find(data77(1:numpt/2)==maxdb);         %输入信号，赋值，找到最大的频率
                                            %  fin=fin+1;
                                            % 如果含直流成分过大，那么fin可能是直流频率
                                            %所以不考虑直流分量
 
Fh= zeros(1:8);   %谐波频率 数组 预设置内存
Ph= zeros(1:8);   %谐波能量 数组 预设置内存
 
for har_num=1:8      %1到15次谐波分量
    tone=rem((har_num*(fin)+1)/numpt,1);  
    %各次谐波对应的能量
    if tone>0.5
            tone=1-tone;      %对称
    end
%Fh=[Fh tone]; %旧版本，每次迭代的时候append 数据，速度慢，现改称预设内存大小，根据数组下标赋值
Fh(har_num)=tone;
%谐波，在Fh中加入tone
if round(tone*numpt)-spanh>0
har_peak=max(data88(round(tone*numpt)-spanh:round(tone*numpt)+spanh));%谐波附近+-spanh最大值
har_bin=find(data88(round(tone*numpt)-spanh:round(tone*numpt)+spanh)==har_peak);%最大值对应频率
    har_bin=har_bin+round(tone*numpt)-spanh-1;
    Ph(har_num)=sum(data88(round(tone*numpt)-spanh:round(tone*numpt)+spanh)); 
 %  har_bin-spanh:har_bin+spanh;
else 
har_peak=max(data88(1:round(tone*numpt)+spanh));%谐波附近+-spanh最大值
har_bin=find(data88(1:round(tone*numpt)+spanh)==har_peak); %最大值对应频率
    har_bin=har_bin+round(tone*numpt)-spanh-1;
    Ph(har_num)=sum(data88(1:har_bin+spanh)); %旧版本，每次迭代的时候append 数据，速度慢，现改称预设内存大小，根据数组下标赋值
end
%某谐波分量
end
%你好
 
Pdc=sum(data88(1:span));                        %直流能量
Ps=bili_factor*sum(data88(fin-span:fin+span));  %信号能量，前后span个都算作信号能量，
Pd=bili_factor*sum(Ph(2:8));                    %谐波能量
Pn=sum(data88(1:numpt/2))-Pdc-Pd/bili_factor-Ps/bili_factor;   
                                                %噪声能量%
 
 
fin_MHz=fin/numpt*Fsample;                      %信号频率
SNR=10*log10(Ps/Pn);                            %信噪比
SINAD=10*log10(Ps/(Pn+Pd));                     %信纳比
SFDR=10*log10(Ph(1)/max(Ph(2:8)));              %无杂散动态范围
ENOB=(SINAD-1.76+20*log10(1/max_voltage))/6.02; %幅度校正后有效位

%   ADC动态特性测试，来自EETOP
function [ENOB, SNDR, SFDR, SNR, THD] = prettyFFT(wave,f_S,maxh,no_annotation,no_plot,baseline);
% Programmed by: Skyler Weaver, Ph.D.
% Date: December 7, 2010
% Version: 1.0
%
% This function plots a very pretty FFT plot and annotates all of the
% relevant data. This is intended to be used by Nyquist guys and expects
% coherently sampled data. Maybe in the future I will modify it to
% automatically detect windowed data for Delta-Sigma or allow two input
% tones. As for now, it is just for single sine-wave test, coherent data.
%
% The full function is:
% [ENOB, SNDR, SFDR, SNR] = prettyFFT(wave,f_S,maxh,no_annotation,no_plot)
%   'wave' can be any length but should be coherently sampled
%       (required)
%   'f_S' is the SAMPLING rate (i.e. f_Nyquist * 2) 
%       (optional, default = 1)
%   'maxh' is the highest harmonic plotted, 0 means all harmonics 
%       (optional, default = 12) NOTE: lowering this value will affect SNR 
%       since SNR is calculated as SNDR with harmonics removed. Setting 
%       maxh to 1 will effectivly set SNR = SNDR. (1 means only the 
%       fundamental is a 'harmonic')
%   'no_annotation' set to anything but 0 to turn off annotation 
%       (optional, default = 0)
%   'no_plot' set to anything but 0 to not create a plot 
%       (optional, default = 0)
%   'baseline' is the minimum value on the y-axis. When set to '0' the
%       y-axis is auto-scaled such that some of the noise floor is
%       displayed. It is useful to set this parameter when comparing two
%       FFT plots by keeping the scale the same.
%       (optional, default = 0)
%
% Here are some usage examples:
% 
%    prettyFFT(some_data)
% 
% In it's most default state, it will take some_data, grab the last 
% 2^integer data points, FFT it, plot it in a pretty way, normalize the 
% x-axis to Nyquist-rate, tag the first 12 harmonics (if above the noise 
% floor), annotate SFDR and SNDR/SNR (if there is room on the plot), draw a
% red line where the effective noise floor is, and returns ENOB. Use this 
% to just plot an FFT from command line and get some useful visual feedback.
% 
%    [ENOB,SNDR,SFDR,SNR]=prettyFFT(some_data)
% 
% does the same thing, but you now have your stats in variables too.
% 
%    prettyFFT(some_data,100e6,15)
% 
% does the same thing, but the x-axis is normalized to 100MHz sampling rate. 
% The default of up to 12 harmonics is overridden to 15. (set to 0, it will
% tag anything significantly above the noise floor, even harmonic 1032 if 
% it is high enough)
% 
%    prettyFFT(some_data,100e6,15,1)
% 
% same thing but annotation is turned off. For journals and stuff
% 
%    prettyFFT(some_data,100e6,15,1,1)
% 
% the extra '1' means it doesn't plot. This is in case you have a loop and 
% you need to get SNR, but you don't want it to keep plotting.
% 
% Feel free to edit, but keep my name at the top. 
%
% Enjoy!
%   -Skyler

if(nargin <= 0)
        disp('prettyFFT: What are you trying to do, exactly?')
        wave = rand(1,100);
        f_S = 1;
        maxh = 1;
        no_annotation = 0;
        no_plot = 0;
        baseline = 0;
end
if(nargin == 1)
        f_S = 1;
        maxh = 12;
        no_annotation = 0;
        no_plot = 0;
        baseline = 0;
end
if(nargin == 2)
        maxh = 12;
        no_annotation = 0;
        no_plot = 0;
        baseline = 0;
end
if(nargin == 3)
        no_annotation = 0;
        no_plot = 0;
        baseline = 0;
end
if(nargin == 4)
        no_plot = 0;
        baseline = 0;
end
if(nargin == 5)
        baseline = 0;
end
if(nargin > 6)
        disp('prettyFFT: Too many arguments, man.')
end

text_y_offset = 4; %height above bar for harmonic # txt (def. = 4)
plev = 9; %dB above noise floor to be considered a harmonic (def. = 9)

[a,b]=size(wave);
if(a>b)
    wave=wave(:,1)';
else
    wave=wave(1,:);
end
fft_ord = floor(log(length(wave))./log(2));

wave = wave(end-2^fft_ord+1:end);
wave=wave-mean(wave); % remove DC offset
f2 = abs(fft(wave)); % fft
f2 = f2(2:floor(length(f2)/2)); % remove bin 1 (DC)

[bin bin] = max(f2);
f2a=[f2(1:(bin-1)) f2((bin+1):end)];
f2a=[f2(1:(bin-1)) mean(f2) f2((bin+1):end)];
step = (bin);
pts = 2*(length(f2)+1);

SNDR = mag2db((f2(bin).^2/(sum(f2.^2)-f2(bin).^2)))/2; % get SNDR (f_in / sum(the rest))
ENOB=(SNDR-1.76)/6.02; % ENOB from SNDR

scaledby = 1./max(f2);
dbf2=mag2db(f2.*scaledby);
dbf2a=[dbf2(1:(bin-1)) dbf2((bin+1):end)];
dbf2a=[dbf2(1:(bin-1)) mean(dbf2) dbf2((bin+1):end)];
[bins bins] =max(dbf2a);
SFDR = -dbf2a(bins);

noise_top = mean(dbf2a)+plev;
noise_floor=mean(dbf2a);
noise_bottom = mean(dbf2a)-plev;
%noise_bottom = min(dbf2a);

% GET HARMONICS
harm = bin;
t=1;
nyqpts=(pts/2-1);
all_harms = harm:step:(harm*nyqpts);
all_harms = mod(all_harms,pts);
all_harms = (pts-all_harms).*(all_harms>nyqpts) ...
            + all_harms.*(all_harms<=nyqpts);
all_harms = all_harms.*(all_harms>0 & all_harms<pts/2) ...
            + (all_harms<=0) ...
            + (all_harms>=pts/2).*nyqpts;
if (maxh==0 || maxh>length(all_harms))
    maxh=length(all_harms);
end
for k=1:maxh
    if(dbf2(all_harms(k)) > noise_top)
        harm(t) = all_harms(k);
        hnum(t) = k;
        t=t+1;
    end
end

% GET REAL SNR
numbins=2.^(fft_ord-1)-1;
non_harm=1:numbins;
non_harm([harm]) = [];
SNR = mag2db((f2(bin).^2/(sum(f2(non_harm).^2))))/2; % get SNR (f_in / sum(the rest))
%SNRpb = db(sqrt((f2(bin).^2/(sum(f2(non_harm).^2))))/numbins)-25 % get SNR (f_in / sum(the rest))
SNRpb = -SNR-3.*(fft_ord-1);
THD = mag2db((sum(f2.^2)-f2(bin).^2-sum(f2(non_harm).^2))/f2(bin).^2)/2;
if(~no_plot)
% MAKE FFT
hold off
f=f_S/nyqpts/2:f_S/nyqpts/2:f_S/2;

h=bar(f,dbf2);   %%%% bar plot choice
% h=plot(f,dbf2,'r*');  %%%% line plot choice

if(~baseline)
xx=max([min([SNRpb noise_bottom])-plev -250]);
else
    xx=baseline;
end

%set(get(h,"BaseLine"),"BaseValue",xx);
set(h,'BaseValue',xx);
set(h,'ShowBaseLine','off');
set(h,'BarWidth',0.1);
set(h,'LineStyle','none');
axis([f(1)/2 f(end)+f(1)/2 xx 0]);

if(~no_annotation)
% HARMONIC RED SQUARES
hold on
plot(f(harm),dbf2(harm),'rs')
text_y_offset = -xx/100*text_y_offset;
if (length(harm) > 2)
    for n=2:length(harm)
        if sum(harm(1:n-1)==harm(n))
            n=n-1; break, end

    end
    if (n<length(harm))
        disp('prettyFFT: Not prime-coherent sampling!')
    end
else
    n=length(harm);
end

% PRINT HARMONICS
%text_y_offset = -xx/100*text_y_offset;
for t=2:n
text(f(harm(t)),dbf2(harm(t))+text_y_offset,num2str(hnum(t)),'HorizontalAlignment','center');
end
hold off

hh=line([f(1)/2 f(end)+f(1)/2],[-SFDR -SFDR]);
set(hh,'LineStyle','--');
set(hh,'Color','k');
hh1=line([f(1)/2 f(end)+f(1)/2],[SNRpb SNRpb]);
set(hh1,'LineStyle','-');
set(hh1,'Color','r');

% where to put SFDR arrow
numbins=floor(pts/2);
dbin=round(numbins/32);
if(numbins>4)
if(bin>numbins/2+dbin*2)
    if(bins<(numbins/4-dbin))|(bins>(numbins/4+dbin))
        abin=round(numbins/4);
    elseif (bins>numbins/4)
        abin=round(numbins/4)-dbin;
    else
        abin=round(numbins/4)+dbin;
    end
else
    if(bins<(3*numbins/4-dbin))|(bins>(3*numbins/4+dbin))
        abin=round(3*numbins/4);
    elseif (bins>3*numbins/4)
        abin=round(3*numbins/4)-dbin;
    else
        abin=round(3*numbins/4)+dbin;
    end
end
else
    abin=12;
end

tempSFDR=SFDR;
if(SFDR > -(.13*xx))
    if(SFDR>250)
        SFDR=250;
    end
dx=f(end)/100;
dy=-xx/30;
x=f(abin);
tdx=max([dx (f(2)-f(1))]);
hh2=line([x-dx x x+dx x x x-dx x x+dx],[-dy 0 -dy 0 -SFDR dy-SFDR -SFDR dy-SFDR]);
set(hh2,'LineStyle','-');
set(hh2,'Color','k');
% text(f(abin)+tdx,-SFDR/2,["SFDR =\n" num2str(tempSFDR,4) 'dB'],'HorizontalAlignment','left');

if(SFDR > -(.25*xx))
infostr=...%['ENOB =\newline' num2str(ENOB,4) ' bits\newline\newline' ...
    ["SNDR =\n" num2str(SNDR,4) "dB \n" ...
    "\nSNR =\n" num2str(SNR,4) 'dB'];
else
infostr=...%['ENOB =\newline' num2str(ENOB,4) ' bits\newline\newline' ...
    ['SNDR = ' num2str(SNDR,4) "dB \n " ...
    'SNR =' num2str(SNR,4) 'dB'];
end
%if(bin<numbins/2)
 %   text(f(bin)+tdx,-SFDR/2,infostr,'HorizontalAlignment','left');
%else
%    text(f(bin)-tdx,-SFDR/2,infostr,'HorizontalAlignment','right');
%end
end
ylabel('dB')
xlabel('Frequency(Hz)')
title('ADC spectrum analysis to obtain SFDR/SNDR/SNR')
SFDR = tempSFDR;
end % end if(~no_annotation)
end % end if(~no_plot)
hold on;
s1=sprintf('SFDR = %4.1fdB\n',SFDR);
s2=sprintf('THD = %4.1fdB\n',THD);
s3=sprintf('SNR   = %4.1fdB\n',SNR);
s4=sprintf('SNDR = %4.1fdB\n',SNDR);
s5=sprintf('ENOB = %4.2fbit\n',ENOB);
text(25,-10,s1);
text(25,-20,s2);
text(25,-30,s3);
text(25,-40,s4);
text(25,-50,s5);
hold off;
end
