function extracted_data = extract_pipeline_data(ideal_data_file, sim_data_file)
    %   如果未提供文件路径，使用默认路径
    if nargin < 1 || isempty(ideal_data_file)
        ideal_data_file = './test_data/pipeline_adc_raw_data.mat';
    end
    if nargin < 2 || isempty(sim_data_file)
        sim_data_file = './test_data/Analog_processing_data.mat';
    end
    %EXTRACT_PIPELINE_DATA 提取流水线ADC数据用于理想模型与晶体管仿真对比
    %   VERSION: v1.0 - 时钟同步数据提取系统
    %
    %   功能描述:
    %   从理想流水线ADC模型数据和Cadence Virtuoso晶体管仿真数据中提取
    %   时钟同步的采样点，用于精确的性能对比分析
    %
    %   核心算法:
    %   1. 晶体管仿真数据(非均匀采样): 检测时钟下降沿，在下降沿前第10个样本点提取
    %   2. 理想模型数据(均匀采样): 每1000个样本点对应一个采样周期
    %   3. 时钟域分离: CLKH域(奇数级) vs CLKS域(偶数级+SHA)
    %   4. 数据对齐: 跳过前19个周期，提取第20-119周期的数据
    %
    %   输入参数:
    %       ideal_data_file - 理想模型数据文件路径 (.mat格式)
    %       sim_data_file - 晶体管仿真数据文件路径 (.mat格式)
    %
    %   输出参数:
    %       extracted_data - 提取后的结构化数据
    %           .SHA.ideal_signal, .SHA.sim_signal - SHA模块对比数据
    %           .STAGE1~8.ideal_signal, .STAGE1~8.sim_signal - 各级对比数据
    %           .extraction_info - 提取过程统计信息
    
        fprintf('=== 流水线ADC数据提取系统 ===\n');
        
        %% 参数设置
        skip_cycles = 20;          % 跳过前20个采样周期（预热期）
        extract_cycles = 100;      % 提取100个采样周期的数据
        samples_per_cycle = 1000;  % 理想模型：每个采样周期1000个样本点
        stable_offset = 10;        % 在时钟下降沿前第10个样本点提取稳定数据
        
        %% 初始化输出结构体
        extracted_data = struct();
        stage_names = {'SHA', 'STAGE1', 'STAGE2', 'STAGE3', 'STAGE4', ...
                       'STAGE5', 'STAGE6', 'STAGE7', 'STAGE8'};
        
        for i = 1:length(stage_names)
            extracted_data.(stage_names{i}) = struct();
            extracted_data.(stage_names{i}).ideal_signal = [];
            extracted_data.(stage_names{i}).sim_signal = [];
        end
        
        %% 第一步：数据加载
        fprintf('\n[步骤1/2] 数据加载...\n');
        
        % 加载理想模型数据 - 严格按照event_driven_pipeline_adc_v4.m定义的数据结构
        fprintf('  正在加载理想模型数据...\n');
        if ~exist(ideal_data_file, 'file')
            error('理想模型数据文件不存在: %s', ideal_data_file);
        end
        
        try
            ideal_data = load(ideal_data_file);
        catch ME
            error('理想模型数据文件读取失败: %s', ME.message);
        end
        
        % 严格验证数据结构 - 必须包含pipeline_raw_data根结构体
        if ~isfield(ideal_data, 'pipeline_raw_data')
            available_fields = fieldnames(ideal_data);
            error(['pipeline_adc_raw_data.mat文件格式错误：缺少根结构体 "pipeline_raw_data"\n' ...
                   '当前文件包含的字段: %s\n' ...
                   '正确格式应包含: pipeline_raw_data'], ...
                   strjoin(available_fields, ', '));
        end
        
        raw_data = ideal_data.pipeline_raw_data;
        
        % 严格验证pipeline_raw_data结构体的必需字段
        required_fields = {'digital_data_struct', 'analog_data_struct', 'time_vector', ...
                          'processing_info', 'params', 'rel_error_params', 'metadata'};
        
        for i = 1:length(required_fields)
            field_name = required_fields{i};
            if ~isfield(raw_data, field_name)
                available_fields = fieldnames(raw_data);
                error(['pipeline_raw_data结构体格式错误：缺少必需字段 "%s"\n' ...
                       '当前结构体包含的字段: %s\n' ...
                       '必需字段列表: %s'], ...
                       field_name, strjoin(available_fields, ', '), strjoin(required_fields, ', '));
            end
        end
        
        % 验证analog_data_struct和time_vector的内部结构
        if ~isfield(raw_data.analog_data_struct, 'signals')
            error('analog_data_struct结构体错误：缺少signals字段');
        end
        
        ideal_time = raw_data.time_vector;
        ideal_analog = raw_data.analog_data_struct;
        
        fprintf('    ✓ 理想模型数据结构验证通过\n');
        fprintf('    总样本数: %d, 模拟信号数: %d\n', ...
                length(ideal_time), length(ideal_analog.signals));
        
        % 加载晶体管仿真数据 - 严格验证simData结构
        fprintf('  正在加载晶体管仿真数据...\n');
        if ~exist(sim_data_file, 'file')
            error('晶体管仿真数据文件不存在: %s', sim_data_file);
        end
        
        try
            sim_data = load(sim_data_file);
        catch ME
            error('晶体管仿真数据文件读取失败: %s', ME.message);
        end
        
        % 严格验证数据结构 - 必须包含simData根结构体
        if ~isfield(sim_data, 'simData')
            available_fields = fieldnames(sim_data);
            error(['Analog_processing_data.mat文件格式错误：缺少根结构体 "simData"\n' ...
                   '当前文件包含的字段: %s\n' ...
                   '正确格式应包含: simData'], ...
                   strjoin(available_fields, ', '));
        end
        
        % 严格验证simData结构体的必需字段
        required_sim_fields = {'signals', 'time'};
        
        for i = 1:length(required_sim_fields)
            field_name = required_sim_fields{i};
            if ~isfield(sim_data.simData, field_name)
                available_fields = fieldnames(sim_data.simData);
                error(['simData结构体格式错误：缺少必需字段 "%s"\n' ...
                       '当前结构体包含的字段: %s\n' ...
                       '必需字段列表: %s'], ...
                       field_name, strjoin(available_fields, ', '), strjoin(required_sim_fields, ', '));
            end
        end
        
        sim_signals = sim_data.simData.signals;
        
        fprintf('    ✓ 晶体管仿真数据结构验证通过\n');
        fprintf('    信号通道数: %d\n', width(sim_signals));
        
        % 验证信号表格格式
        if ~istable(sim_signals) || isempty(sim_signals)
            error('simData.signals数据格式错误：必须是非空的数据表格');
        end
        
        % 列出信号通道（用于调试）
        fprintf('    信号通道列表:\n');
        signal_names = sim_signals.Properties.VariableNames;
        for i = 1:length(signal_names)
            fprintf('      %d: %s\n', i, signal_names{i});
        end
        
        fprintf('✓ 数据加载完成\n');
    
    %% 第二步：数据提取
    fprintf('\n[步骤2/2] 数据提取...\n');
    
    % 提取理想模型数据（均匀采样）
    fprintf('  正在提取理想模型数据（均匀采样）...\n');
    
    % 计算起始索引：第20个周期的开始
    start_sample = skip_cycles * samples_per_cycle + 1;
    
    % 验证数据长度
    total_needed_samples = (skip_cycles + extract_cycles) * samples_per_cycle;
    if length(ideal_time) < total_needed_samples
        error('理想模型数据不足：需要%d样本，实际%d样本', ...
              total_needed_samples, length(ideal_time));
    end
    
    % 定义时钟域分配（与仿真数据处理保持一致）
    clkh_stages = {'STAGE1', 'STAGE3', 'STAGE5', 'STAGE7'};  % CLKH控制的模块
    clks_stages = {'SHA', 'STAGE2', 'STAGE4', 'STAGE6', 'STAGE8'};  % CLKS控制的模块
    
    % 计算不同时钟域的采样位置
    % CLKH域：在每个周期的第990个样本点提取（下降沿前第10个点）
    clkh_stable_position = samples_per_cycle - stable_offset;
    
    % CLKS域：考虑到CLKS相对于CLKH提前半个周期，在每个周期的第490个样本点提取
    % 根据generate_timebase.m中的相位关系：CLKS延迟0.05/fs，CLKH延迟0.55/fs
    % CLKH相对于CLKS延迟了0.5个周期，所以CLKS域的稳定时间点应该提前半个周期
    clks_stable_position = samples_per_cycle / 2 - stable_offset;
    
    % 验证信号数量
    if length(ideal_analog.signals) < 9
        error('理想模型信号数量不足：需要9个信号，实际%d个', length(ideal_analog.signals));
    end
    
    % 为每个级别分别生成对应时钟域的采样索引并提取数据
    fprintf('    开始为各级生成采样索引并提取数据...\n');
    
    for stage_idx = 1:9
        stage_name = stage_names{stage_idx};
        
        % 确定该级所属的时钟域
        if ismember(stage_name, clkh_stages)
            stable_position = clkh_stable_position;
            clock_domain = 'CLKH';
        else
            stable_position = clks_stable_position;
            clock_domain = 'CLKS';
        end
        
        % 为该级生成采样索引
        stage_indices = [];
        for cycle = 0:(extract_cycles-1)
            cycle_start = start_sample + cycle * samples_per_cycle;
            extract_idx = cycle_start + stable_position - 1;  % -1因为MATLAB索引从1开始
            if extract_idx > length(ideal_time)
                error('理想模型提取索引超出数据范围：索引%d，数据长度%d', extract_idx, length(ideal_time));
            end
            stage_indices(end+1) = extract_idx;
        end
        
        % 验证并提取信号数据
        stage_signal = ideal_analog.signals(stage_idx).values;
        if length(stage_signal) < max(stage_indices)
            error('理想模型第%d级信号长度不足：需要%d，实际%d', ...
                  stage_idx, max(stage_indices), length(stage_signal));
        end
        
        % 提取信号数据
        extracted_signal = stage_signal(stage_indices);
        extracted_data.(stage_name).ideal_signal = extracted_signal;
        
        fprintf('      %s: 采样位置=%d, 时钟域=%s, 提取%d个样本\n', ...
                stage_name, stable_position, clock_domain, length(extracted_signal));
    end
    
    fprintf('    理想模型数据提取完成\n');
    
    % 提取晶体管仿真数据（非均匀采样）
    fprintf('  正在提取晶体管仿真数据（非均匀采样）...\n');
    
    % 查找时钟信号
    clkh_signal = [];
    clks_signal = [];
    
    for i = 1:width(sim_signals)
        signal_name = sim_signals.Properties.VariableNames{i};
        if contains(signal_name, 'CLKH_V_')
            clkh_signal = sim_signals.(signal_name);
            fprintf('    找到CLKH信号: %s\n', signal_name);
        elseif contains(signal_name, 'CLKS_V_')
            clks_signal = sim_signals.(signal_name);
            fprintf('    找到CLKS信号: %s\n', signal_name);
        end
    end
    
    if isempty(clkh_signal) || isempty(clks_signal)
        error('无法找到时钟信号CLKH_V_或CLKS_V_');
    end
    
    % 检测时钟下降沿（使用新的真实电路特性检测算法）
    [clkh_falling_edges, clkh_stable_indices] = detect_falling_edges(clkh_signal);
    [clks_falling_edges, clks_stable_indices] = detect_falling_edges(clks_signal);
    
    fprintf('    检测到CLKH下降沿: %d个, CLKS下降沿: %d个\n', ...
            length(clkh_falling_edges), length(clks_falling_edges));
    
    % 验证下降沿数量
    min_required_edges = skip_cycles + extract_cycles;
    if length(clkh_falling_edges) < min_required_edges || length(clks_falling_edges) < min_required_edges
        error('时钟下降沿数量不足：需要%d个，CLKH有%d个，CLKS有%d个', ...
              min_required_edges, length(clkh_falling_edges), length(clks_falling_edges));
    end
    
    % 使用前面定义的时钟域分配（复用变量，避免重复定义）
    
    % 提取各级仿真信号
    for stage_idx = 1:length(stage_names)
        stage_name = stage_names{stage_idx};
        
        % 确定该级使用的时钟域稳定样本索引和跳过周期数
        if ismember(stage_name, clkh_stages)
            stable_indices = clkh_stable_indices;
            clock_type = 'CLKH';
            % CLKH域多跳过1个周期以实现数据对齐
            skip_cycles_for_domain = skip_cycles + 1;
        else
            stable_indices = clks_stable_indices;
            clock_type = 'CLKS';
            % CLKS域保持原有跳过周期数
            skip_cycles_for_domain = skip_cycles;
        end
        
        % 查找对应的仿真信号
        sim_signal_data = [];
        
        if strcmp(stage_name, 'SHA')
            signal_label = 'SHA_V_';
        else
            signal_label = sprintf('%s_V_', stage_name);
        end
        
        for i = 1:width(sim_signals)
            signal_name = sim_signals.Properties.VariableNames{i};
            if contains(signal_name, signal_label)
                sim_signal_data = sim_signals.(signal_name);
                fprintf('    找到%s信号: %s (时钟域: %s, 跳过周期: %d)\n', ...
                        stage_name, signal_name, clock_type, skip_cycles_for_domain);
                break;
            end
        end
        
        if isempty(sim_signal_data)
            % 提供更详细的错误信息，列出可用的信号标签
            available_labels = {};
            for j = 1:width(sim_signals)
                available_labels{end+1} = sim_signals.Properties.VariableNames{j};
            end
            error('未找到%s的仿真信号（搜索标签: %s）\n可用信号: %s', ...
                  stage_name, signal_label, strjoin(available_labels, ', '));
        end
        
        % 使用检测到的稳定样本点直接提取数据，传入对应时钟域的跳过周期数
        extracted_signal = extract_stable_samples_direct(sim_signal_data, ...
                                                        stable_indices, skip_cycles_for_domain, ...
                                                        extract_cycles);
        
        extracted_data.(stage_name).sim_signal = extracted_signal;
    end
    
    fprintf('    晶体管仿真数据提取完成\n');
    
    %% 添加提取统计信息
    extracted_data.extraction_info = struct();
    extracted_data.extraction_info.skip_cycles = skip_cycles;
    extracted_data.extraction_info.extract_cycles = extract_cycles;
    extracted_data.extraction_info.samples_per_cycle = samples_per_cycle;
    extracted_data.extraction_info.stable_offset = stable_offset;
    extracted_data.extraction_info.total_extracted_samples = extract_cycles;
    extracted_data.extraction_info.clkh_stages = clkh_stages;
    extracted_data.extraction_info.clks_stages = clks_stages;
    % 添加时钟域采样位置信息
    extracted_data.extraction_info.clkh_sampling_position = clkh_stable_position;
    extracted_data.extraction_info.clks_sampling_position = clks_stable_position;
    extracted_data.extraction_info.phase_aware_extraction = true;
    
    fprintf('✓ 数据提取完成\n');
    
    fprintf('\n=== 数据提取完成 ===\n');
    fprintf('提取统计:\n');
    fprintf('  跳过周期: %d\n', skip_cycles);
    fprintf('  提取周期: %d\n', extract_cycles);
    fprintf('  每个模块提取样本数: %d\n', extract_cycles);
    
    % 验证数据长度一致性
    fprintf('\n数据长度验证:\n');
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        ideal_len = length(extracted_data.(stage_name).ideal_signal);
        sim_len = length(extracted_data.(stage_name).sim_signal);
        fprintf('  %s: 理想%d, 仿真%d', stage_name, ideal_len, sim_len);
        if ideal_len == sim_len
            fprintf(' ✓\n');
        else
            error('%s模块数据长度不匹配：理想%d，仿真%d', stage_name, ideal_len, sim_len);
        end
    end
end

function [falling_edges, stable_sample_indices] = detect_falling_edges(signal)
%DETECT_FALLING_EDGES 检测真实电路时钟信号的下降沿
%   输入参数:
%       signal - 时钟信号向量
%   输出参数:
%       falling_edges - 下降沿开始位置的数组索引
%       stable_sample_indices - 每个下降沿前第10个稳定样本点的索引数组

    % 真实电路参数设置（1.8V工艺）
    V_HIGH_MIN = 1.4;        % 高电平阈值：>1.4V为稳定高电平
    V_LOW_MAX = 0.4;         % 低电平阈值：<0.4V为稳定低电平
    STABLE_OFFSET = 10;      % 在下降沿前第10个样本点提取稳定数据
    MIN_STABLE_SAMPLES = 5;  % 最少连续稳定样本数才确认为稳定状态
    
    % 状态定义
    STATE_UNKNOWN = 0;
    STATE_STABLE_HIGH = 1;
    STATE_TRANSITION = 2;
    STATE_STABLE_LOW = 3;
    
    % 初始化
    falling_edges = [];
    stable_sample_indices = [];
    current_state = STATE_UNKNOWN;
    stable_high_start = 0;   % 稳定高电平区间的起始索引
    stable_counter = 0;      % 连续稳定样本计数器

    % 状态机算法：逐点分析信号状态
    for i = 1:length(signal)
        current_voltage = signal(i);
        
        switch current_state
            case {STATE_UNKNOWN, STATE_STABLE_LOW, STATE_TRANSITION}
                % 检测进入稳定高电平状态
                if current_voltage > V_HIGH_MIN
                    if current_state ~= STATE_STABLE_HIGH
                        % 开始新的稳定高电平区间
                        stable_high_start = i;
                        stable_counter = 1;
                        current_state = STATE_STABLE_HIGH;
                    else
                        stable_counter = stable_counter + 1;
                    end
                else
                    % 重置状态
                    current_state = STATE_UNKNOWN;
                    stable_counter = 0;
                end
                
            case STATE_STABLE_HIGH
                if current_voltage > V_HIGH_MIN
                    % 继续保持稳定高电平
                    stable_counter = stable_counter + 1;
                else
                    % 检测到下降沿开始
                    if stable_counter >= MIN_STABLE_SAMPLES
                        % 确认前面有足够的稳定高电平样本
                        falling_edge_start = i;  % 下降沿开始位置
                        
                        % 计算稳定样本点索引（下降沿前第10个点）
                        stable_sample_idx = falling_edge_start - STABLE_OFFSET;
                        
                        % 验证稳定样本点在有效范围内
                        if stable_sample_idx > 0 && stable_sample_idx >= stable_high_start
                            % 双重验证：确保提取的样本点确实在稳定高电平区间
                            if signal(stable_sample_idx) > V_HIGH_MIN
                                falling_edges(end+1) = falling_edge_start;
                                stable_sample_indices(end+1) = stable_sample_idx;
                                
                                fprintf('        下降沿%d: 位置=%d(%.3fV→%.3fV), 稳定样本=%d(%.3fV)\n', ...
                                        length(falling_edges), falling_edge_start, ...
                                        signal(falling_edge_start-1), current_voltage, ...
                                        stable_sample_idx, signal(stable_sample_idx));
                            else
                                fprintf('        警告: 位置%d处稳定样本点电压不足(%.3fV < %.1fV)\n', ...
                                        stable_sample_idx, signal(stable_sample_idx), V_HIGH_MIN);
                            end
                        else
                            fprintf('        警告: 位置%d处稳定样本点索引无效(范围: %d-%d)\n', ...
                                    i, stable_high_start, falling_edge_start-1);
                        end
                    end
                    
                    % 转换到转换状态
                    current_state = STATE_TRANSITION;
                    stable_counter = 0;
                end
        end
        
        % 检测转换状态到稳定低电平
        if current_state == STATE_TRANSITION && current_voltage < V_LOW_MAX
            current_state = STATE_STABLE_LOW;
        end
    end
    
    fprintf('      检测完成: 找到%d个有效下降沿\n', length(falling_edges));
    
    % 验证输出数组长度一致性
    if length(falling_edges) ~= length(stable_sample_indices)
        error('下降沿检测算法内部错误：输出数组长度不匹配');
    end
    
    % 转换为列向量
    falling_edges = falling_edges(:);
    stable_sample_indices = stable_sample_indices(:);
end

function extracted_signal = extract_stable_samples_direct(signal_data, stable_indices, skip_cycles, extract_cycles)
%EXTRACT_STABLE_SAMPLES_DIRECT 直接使用检测到的稳定样本索引提取数据
%   使用detect_falling_edges返回的稳定样本索引，跳过重复计算
%   确保提取的是已验证的稳定状态下的信号值
%
%   输入参数:
%       signal_data - 目标信号数据向量
%       stable_indices - 已检测到的稳定样本点索引数组
%       skip_cycles - 跳过的前导周期数
%       extract_cycles - 需要提取的周期数
%
%   输出参数:
%       extracted_signal - 提取的信号数据列向量

    extracted_signal = [];
    
    % 从第(skip_cycles+1)个稳定样本开始提取
    start_idx = skip_cycles + 1;
    end_idx = min(skip_cycles + extract_cycles, length(stable_indices));
    
    fprintf('        从稳定样本索引%d开始，提取%d个样本点\n', start_idx, end_idx - start_idx + 1);
    
    for i = start_idx:end_idx
        stable_sample_idx = stable_indices(i);
        
        % 验证索引有效性
        if stable_sample_idx <= 0 || stable_sample_idx > length(signal_data)
            error('稳定样本索引%d超出信号数据范围[1, %d]', ...
                  stable_sample_idx, length(signal_data));
        end
        
        extracted_signal(end+1) = signal_data(stable_sample_idx);
    end
    
    % 验证提取的样本数
    expected_samples = end_idx - start_idx + 1;
    if length(extracted_signal) ~= expected_samples
        error('提取的样本数(%d)与预期(%d)不符', length(extracted_signal), expected_samples);
    end
    
    % 确保返回列向量
    extracted_signal = extracted_signal(:);
    
    fprintf('        成功提取%d个稳定样本点\n', length(extracted_signal));
end
