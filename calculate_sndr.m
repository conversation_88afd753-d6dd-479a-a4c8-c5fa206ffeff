function [sndr, enob, snr, sfdr, hd_values] = calculate_sndr(pipeline_output, fs, f_in)
%CALCULATE_SNDR 计算流水线ADC的动态特性指标（适配版本）
%   [sndr, enob, snr, sfdr, hd_values] = CALCULATE_SNDR(pipeline_output, fs, f_in)
%   从流水线ADC输出中提取有效数据点，计算SNDR, ENOB, SNR, SFDR以及谐波失真
%
%   输入参数:
%       pipeline_output - 流水线ADC最终输出信号（经过数字校正后的模拟值）
%       fs - 采样频率 (Hz)
%       f_in - 输入信号频率 (Hz)
%
%   输出参数:
%       sndr - 信噪比及失真比 (dB)
%       enob - 有效位数 (bits)
%       snr - 信噪比 (dB)
%       sfdr - 无杂散动态范围 (dB)
%       hd_values - 各次谐波相对基波的大小 (dB)
%
%   数据处理特点:
%   1. 跳过前100个采样周期，提取有效采样点进行FFT分析
%   2. 基于相干采样原则直接计算频谱，必要时应用窗函数
%   3. 采用与calculate_dynamic_performance.m相同的数据提取逻辑

fprintf('=== 流水线ADC动态特性计算（SNDR适配版本）===\n');

% 参数验证
if nargin < 3
    error('至少需要提供pipeline_output、fs、f_in三个参数');
end

%% 第一步：从流水线ADC输出中提取有效数据点
fprintf('\n[步骤1/3] 提取有效数据点...\n');

% 参数配置（与calculate_dynamic_performance.m保持一致）
skip_cycles = 100;              % 跳过前100个采样周期
target_samples = 1024;          % 提取1024个样本点用于FFT

% 基于均匀采样特性提取有效数据点
valid_sample_indices = extract_valid_samples(pipeline_output, skip_cycles);

% 验证有效样本数量
if length(valid_sample_indices) < target_samples
    error('有效样本数量不足：需要%d个，实际%d个', ...
          target_samples, length(valid_sample_indices));
end

% 从已预处理的有效样本中提取样本用于FFT分析
selected_indices = valid_sample_indices(1:target_samples);
adc_output = pipeline_output(selected_indices);

fprintf('  提取%d个有效样本用于FFT分析\n', length(adc_output));

%% 第二步：数据预处理
fprintf('\n[步骤2/3] 数据预处理...\n');

% 移除直流偏移
adc_output = adc_output - mean(adc_output);

% 记录样本信息
N = length(adc_output);
max_voltage = max(abs(adc_output));

fprintf('  样本数量: %d, 最大电压值: %.6f V\n', N, max_voltage);

% 验证相干采样条件
M = round(f_in * N / fs);  % 实际记录的信号周期数
coherent_error = abs(M - (f_in * N / fs));

if coherent_error > 1e-6
    fprintf('  警告: 相干采样误差: %.9f，应用Hanning窗\n', coherent_error);
    window = hanning(N);
    adc_output = adc_output .* window;
else
    fprintf('  满足相干采样条件，误差: %.9f\n', coherent_error);
end

%% 第三步：FFT频谱分析和动态特性计算
fprintf('\n[步骤3/3] FFT频谱分析和动态特性计算...\n');

% 获取数据长度
numpt = length(adc_output);

% 执行FFT分析
fft_result = fft(adc_output);
fft_mag = abs(fft_result);
fft_power = (fft_mag.^2)/numpt^2;  % 归一化功率谱

% 计算dB表示的频谱（用于可视化）
fft_db = 20*log10(fft_mag/numpt + eps);  % 添加eps防止log(0)

% ----- 基频检测 -----
% 精确确定基频bin位置
bin_estimate = round(f_in/fs*numpt) + 1;
search_range = max(2, bin_estimate-3):min(numpt/2, bin_estimate+3);

if isempty(search_range)
    error('频率搜索范围无效，f_in可能过高或数据长度不足。');
end

[~, peak_index] = max(fft_mag(search_range));
fundamental_bin = search_range(peak_index);

fprintf('基频bin位置: %d (理论位置: %.1f)\n', fundamental_bin, f_in/fs*numpt+1);

% ----- 功率计算参数 -----
% 确定基频和谐波搜索范围参数
span = 3;  % 基频和谐波功率积分半径
spanh = 3;  % 谐波搜索范围

% ----- 直流和基波功率计算 -----
% 计算直流功率（未使用）
Pdc = sum(fft_power(1:span));

% 计算基波功率 (包含泄漏)
Ps_range = max(1, fundamental_bin-span):min(numpt/2, fundamental_bin+span);
Ps = sum(fft_power(Ps_range));
fprintf('基频频率: %.3f kHz，功率: %.6f\n', f_in/1000, Ps);

% ----- 谐波检测和功率计算 -----
% 初始化谐波功率数组
Ph = zeros(1, 9);  % 谐波功率
harmonic_bins = zeros(1, 9);  % 谐波bin位置

% 计算2-10次谐波功率
for har_num = 2:10
    % 计算谐波理论bin位置
    har_bin_theory = har_num * (fundamental_bin-1) + 1;

    % 处理频谱折叠
    while har_bin_theory > numpt
        har_bin_theory = 2*numpt - har_bin_theory;
    end

    % 处理超过奈奎斯特频率，计算镜像频率
    if har_bin_theory > numpt/2
        har_bin_theory = numpt - har_bin_theory + 2;
    end

    % 在理论位置附近搜索峰值
    search_start = max(2, round(har_bin_theory)-spanh);
    search_end = min(numpt/2, round(har_bin_theory)+spanh);
    search_range = search_start:search_end;

    if ~isempty(search_range)
        % 寻找峰值及其位置
        [~, peak_idx] = max(fft_power(search_range));
        har_bin = search_range(peak_idx);
        harmonic_bins(har_num-1) = har_bin;  % 保存bin位置

        % 计算谐波功率
        h_range = max(2, har_bin-span):min(numpt/2, har_bin+span);
        har_power = sum(fft_power(h_range));
        Ph(har_num-1) = har_power;

        fprintf('第%d次谐波 - 理论bin: %.1f, 实际bin: %d, 功率: %.9f\n', ...
                har_num, har_bin_theory, har_bin, har_power);
    else
        Ph(har_num-1) = 0;  % 未找到有效谐波
    end
end

% ----- 噪声和失真功率计算 -----
% 计算总谐波失真（取2-5次谐波）
Pd = sum(Ph(1:min(4, length(Ph))));

% 计算总功率（排除直流）
Ptotal = sum(fft_power(2:numpt/2));

% 计算噪声功率（总功率减去基波和谐波功率）
noise_bins = ones(1, numpt/2-1);

% 排除基波对应的bin
for bin = Ps_range
    if bin >= 2 && bin <= numpt/2
        noise_bins(bin-1) = 0;
    end
end

% 排除各次谐波对应的bin
for i = 1:length(harmonic_bins)
    if harmonic_bins(i) >= 2 && harmonic_bins(i) <= numpt/2
        har_range = max(2, harmonic_bins(i)-span):min(numpt/2, harmonic_bins(i)+span);
        noise_bins(har_range-1) = 0;
    end
end

% 统计剩余的噪声功率
Pn = sum(fft_power(2:numpt/2) .* noise_bins');

fprintf('噪声功率: %.9f, 谐波功率: %.9f, 总功率: %.9f\n', Pn, Pd, Ptotal);

% ----- 性能指标计算 -----
% 计算SNR、SNDR、SFDR、ENOB
snr = 10*log10(Ps/Pn);
sndr = 10*log10(Ps/(Pn+Pd));

% 计算SFDR（基于最大谐波或杂散）
if any(Ph > 0)
    [max_harmonic, max_har_idx] = max(Ph);
    sfdr = 10*log10(Ps/max_harmonic);
    fprintf('最大谐波: HD%d = %.6f\n', max_har_idx+1, max_harmonic);
else
    sfdr = 100;  % 没有检测到谐波时的默认值
end

% 计算ENOB（有效位数）
enob = (sndr - 1.76)/6.02;

% 计算各次谐波相对基波的大小（dB）
hd_values = 10*log10(Ph/Ps);

% 输出结果
fprintf('\n=== 动态特性指标结果 ===\n');
fprintf('SNR  = %.2f dB\n', snr);
fprintf('SNDR = %.2f dB\n', sndr);
fprintf('SFDR = %.2f dB\n', sfdr);
fprintf('ENOB = %.2f bits\n', enob);
fprintf('THD  = %.2f dB\n', 10*log10(Pd/Ps));

% ----- 可选的频谱可视化 -----
if nargout == 0
    % 绘制频谱图
    freq_axis = (0:numpt/2-1) * fs/numpt;
    figure;
    plot(freq_axis, fft_db(1:numpt/2));
    title('FFT 频谱分析');
    xlabel('频率 (Hz)');
    ylabel('幅度 (dB)');
    grid on;

    % 标注基频和谐波
    hold on;
    plot(freq_axis(fundamental_bin), fft_db(fundamental_bin), 'ro', 'MarkerSize', 10);
    text(freq_axis(fundamental_bin), fft_db(fundamental_bin)+3, 'f_0', 'FontSize', 12);

    for i = 1:min(5, length(harmonic_bins))
        if harmonic_bins(i) > 0 && harmonic_bins(i) <= numpt/2
            plot(freq_axis(harmonic_bins(i)), fft_db(harmonic_bins(i)), 'go', 'MarkerSize', 8);
            text(freq_axis(harmonic_bins(i)), fft_db(harmonic_bins(i))+2, sprintf('HD%d', i+1), 'FontSize', 10);
        end
    end
    hold off;
end

fprintf('\n=== 流水线ADC动态特性计算完成 ===\n');
end

function valid_sample_indices = extract_valid_samples(pipeline_output, skip_cycles)
%EXTRACT_VALID_SAMPLES 从流水线ADC输出中提取有效数据点
%   与calculate_dynamic_performance.m中的实现保持一致

    % 参数设置
    samples_per_cycle = 1000;
    stable_offset = 100;
    stable_position = samples_per_cycle - stable_offset;

    % 计算总采样周期数
    total_samples = length(pipeline_output);
    total_cycles = floor(total_samples / samples_per_cycle);

    % 验证数据长度
    if total_cycles < skip_cycles + 1
        error('数据长度不足：需要至少%d个周期，实际%d个周期', ...
              skip_cycles + 1, total_cycles);
    end

    % 计算可提取的周期数
    available_cycles = total_cycles - skip_cycles;

    % 预分配输出数组提高性能
    valid_sample_indices = zeros(1, available_cycles);
    valid_count = 0;

    % 直接计算采样索引
    start_sample = skip_cycles * samples_per_cycle + 1;

    for cycle = 0:(available_cycles-1)
        % 计算该周期的稳定采样点索引
        cycle_start = start_sample + cycle * samples_per_cycle;
        extract_idx = cycle_start + stable_position - 1;

        % 验证索引有效性
        if extract_idx > 0 && extract_idx <= total_samples
            valid_count = valid_count + 1;
            valid_sample_indices(valid_count) = extract_idx;
        end
    end

    % 截取有效部分
    valid_sample_indices = valid_sample_indices(1:valid_count);

    % 转换为列向量
    valid_sample_indices = valid_sample_indices(:);
end